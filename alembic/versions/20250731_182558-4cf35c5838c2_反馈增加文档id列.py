"""反馈增加文档ID列

Revision ID: 4cf35c5838c2
Revises: 39c8154aa307
Create Date: 2025-07-31 18:25:58.365845

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "4cf35c5838c2"
down_revision: Union[str, None] = "39c8154aa307"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "entity_recommend_feedback",
        sa.Column("document_id", sa.String(length=64), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("entity_recommend_feedback", "document_id")
    # ### end Alembic commands ###
