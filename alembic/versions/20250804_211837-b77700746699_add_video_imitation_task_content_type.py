"""add_video_imitation_task_content_type

Revision ID: b77700746699
Revises: fcaaadfc3606
Create Date: 2025-08-04 21:18:37.496177

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "b77700746699"
down_revision: Union[str, None] = "fcaaadfc3606"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat",
        "content_type",
        existing_type=sa.Enum(
            "init",
            "command",
            "disasm_request",
            "derive_form",
            "derive_confirm",
            "knowledge_base",
            "card",
            "disasm_report",
            "choice",
            "decision",
            "clear_context",
            "product_report",
            "normal",
            name="contenttype",
        ),
        type_=sa.Enum(
            "init",
            "normal",
            "command",
            "decision",
            "disasm_request",
            "disasm_report",
            "knowledge_base",
            "product_report",
            "choice",
            "derive_form",
            "derive_confirm",
            "clear_context",
            "card",
            "video_imitation_task",
            name="contenttype",
        ),
        existing_comment="文本类型",
        existing_nullable=False,
        existing_server_default=sa.text("'normal'"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat",
        "content_type",
        existing_type=sa.Enum(
            "init",
            "normal",
            "command",
            "decision",
            "disasm_request",
            "disasm_report",
            "knowledge_base",
            "product_report",
            "choice",
            "derive_form",
            "derive_confirm",
            "clear_context",
            "card",
            "video_imitation_task",
            name="contenttype",
        ),
        type_=sa.Enum(
            "init",
            "command",
            "disasm_request",
            "derive_form",
            "derive_confirm",
            "knowledge_base",
            "card",
            "disasm_report",
            "choice",
            "decision",
            "clear_context",
            "product_report",
            "normal",
            name="contenttype",
        ),
        existing_comment="文本类型",
        existing_nullable=False,
        existing_server_default=sa.text("'normal'"),
    )
    # ### end Alembic commands ###
