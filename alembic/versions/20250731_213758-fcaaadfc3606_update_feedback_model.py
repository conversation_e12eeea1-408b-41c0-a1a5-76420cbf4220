"""update_feedback_model

Revision ID: fcaaadfc3606
Revises: 4cf35c5838c2
Create Date: 2025-07-31 21:37:58.376655

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "fcaaadfc3606"
down_revision: Union[str, None] = "4cf35c5838c2"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "entity_recommend_feedback",
        "chat_id",
        existing_type=mysql.VARCHAR(length=64),
        nullable=True,
    )
    op.alter_column(
        "entity_recommend_feedback",
        "conversation_id",
        existing_type=mysql.VARCHAR(length=64),
        nullable=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "entity_recommend_feedback",
        "conversation_id",
        existing_type=mysql.VARCHAR(length=64),
        nullable=False,
    )
    op.alter_column(
        "entity_recommend_feedback",
        "chat_id",
        existing_type=mysql.VARCHAR(length=64),
        nullable=False,
    )
    # ### end Alembic commands ###
