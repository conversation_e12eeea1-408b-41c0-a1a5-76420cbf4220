#!/usr/bin/env python3
"""
测试数据比较逻辑
"""

import json
from datetime import datetime


def normalize_record(record):
    """标准化记录，排除时间字段"""
    return {
        "phone": record.get("phone", ""),
        "customer_name": record.get("customer_name", ""),
        "env_source": record.get("env_source", ""),
        "is_internal_user": record.get("is_internal_user", False),
        "additional": record.get("additional", "{}"),
    }


def compare_data(feishu_records, ch_records):
    """比较飞书数据和 ClickHouse 数据是否一致"""

    # 按环境+手机号排序并标准化
    feishu_normalized = sorted(
        [normalize_record(r) for r in feishu_records],
        key=lambda x: (x["env_source"], x["phone"]),
    )
    ch_normalized = sorted(
        [normalize_record(r) for r in ch_records],
        key=lambda x: (x["env_source"], x["phone"]),
    )

    # 比较数据
    is_same = feishu_normalized == ch_normalized

    if is_same:
        print("✅ 数据一致，无需同步")
    else:
        print(
            f"❌ 数据不一致 - 飞书: {len(feishu_normalized)} 条, ClickHouse: {len(ch_normalized)} 条"
        )

        # 详细差异分析
        if len(feishu_normalized) != len(ch_normalized):
            print("📊 记录数量不同")
        else:
            # 找出具体的差异
            for i, (f_record, c_record) in enumerate(
                zip(feishu_normalized, ch_normalized)
            ):
                if f_record != c_record:
                    print(f"🔍 记录 {i+1} 不同:")
                    print(f"  飞书: {f_record}")
                    print(f"  ClickHouse: {c_record}")

                    # 逐字段比较
                    for key in f_record:
                        if f_record[key] != c_record.get(key):
                            print(
                                f"    字段 '{key}' 不同: '{f_record[key]}' vs '{c_record.get(key)}'"
                            )
                    break

    return is_same


def test_identical_data():
    """测试相同数据"""
    print("=== 测试相同数据 ===")

    # 飞书数据（包含时间字段）
    feishu_data = [
        {
            "phone": "13800138000",
            "customer_name": "张三公司",
            "env_source": "prod",
            "is_internal_user": False,
            "additional": json.dumps(
                {"ecommerce_category": "服装"}, ensure_ascii=False
            ),
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        }
    ]

    # ClickHouse 数据（不包含时间字段）
    ch_data = [
        {
            "phone": "13800138000",
            "customer_name": "张三公司",
            "env_source": "prod",
            "is_internal_user": False,
            "additional": json.dumps(
                {"ecommerce_category": "服装"}, ensure_ascii=False
            ),
        }
    ]

    result = compare_data(feishu_data, ch_data)
    assert result == True, "相同数据应该返回 True"


def test_different_data():
    """测试不同数据"""
    print("\n=== 测试不同数据 ===")

    feishu_data = [
        {
            "phone": "13800138000",
            "customer_name": "张三公司",
            "env_source": "prod",
            "is_internal_user": False,
            "additional": json.dumps(
                {"ecommerce_category": "服装"}, ensure_ascii=False
            ),
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        }
    ]

    ch_data = [
        {
            "phone": "13800138000",
            "customer_name": "李四公司",  # 不同的客户名称
            "env_source": "prod",
            "is_internal_user": False,
            "additional": json.dumps(
                {"ecommerce_category": "服装"}, ensure_ascii=False
            ),
        }
    ]

    result = compare_data(feishu_data, ch_data)
    assert result == False, "不同数据应该返回 False"


def test_multi_env_data():
    """测试多环境数据"""
    print("\n=== 测试多环境数据 ===")

    feishu_data = [
        {
            "phone": "13800138000",
            "customer_name": "张三公司(测试)",
            "env_source": "staging",
            "is_internal_user": False,
            "additional": json.dumps(
                {"ecommerce_category": "服装"}, ensure_ascii=False
            ),
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        },
        {
            "phone": "13800138000",
            "customer_name": "张三公司(正式)",
            "env_source": "prod",
            "is_internal_user": False,
            "additional": json.dumps(
                {"ecommerce_category": "数码"}, ensure_ascii=False
            ),
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        },
    ]

    ch_data = [
        {
            "phone": "13800138000",
            "customer_name": "张三公司(正式)",
            "env_source": "prod",
            "is_internal_user": False,
            "additional": json.dumps(
                {"ecommerce_category": "数码"}, ensure_ascii=False
            ),
        },
        {
            "phone": "13800138000",
            "customer_name": "张三公司(测试)",
            "env_source": "staging",
            "is_internal_user": False,
            "additional": json.dumps(
                {"ecommerce_category": "服装"}, ensure_ascii=False
            ),
        },
    ]

    result = compare_data(feishu_data, ch_data)
    assert result == True, "多环境相同数据应该返回 True"


if __name__ == "__main__":
    test_identical_data()
    test_different_data()
    test_multi_env_data()
    print("\n🎉 所有数据比较测试通过！")
