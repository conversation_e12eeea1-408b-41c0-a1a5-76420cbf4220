运营监控
下面的监控内容，根据分析角度不同，应让 AI 生成3~5 个 view。
然后 superset 上，创建可视化图表或表格。

1. 使用粘性
基础使用：
- 日活跃度：每日使用用户/总开通用户，目标≥40%

数据解释：
- 每日使用用户：即日活用户，对应于任意 event_type
- 总开通用户:  从用户白名单表取（排除内部用户）


- 平均日查询次数（电商助手、内容助手）：目标≥8次/用户/天
后端事件：housekeeper:query。参数：助手类型、发送的消息内容、conversation_id、历史消息长度（用户发过几次消息，注意上下文可能清除了）。

- 高频用户占比：每日查询≥15次的用户比例，目标≥20%
同上

- 连续活跃天数：用户最长连续使用天数，目标≥5天


- 周活跃率：每周至少使用3次的用户占比，目标≥70%


2. 使用深度
会话质量：
- 平均会话轮数：每次对话的问答轮数，目标≥3轮
后端事件：housekeeper:query。

- 会话时长：单次会话持续时间，目标≥10分钟
先画出个表格吧（筛选出 housekeeper:query）：客户名称、事件时间、conversation_id、问题内容、历史消息长度

- 追问率：用户在AI回答后继续追问的比例，目标≥60%
查询 event_type=housekeeper:query, 统计 count(历史消息长度 > 0) / count(总数)

- 停止回答使用率：目标≤5%（高了说明回答质量差）
前端事件： STOP_ANSWER

查询复杂度(需要识别AI处理维度)
- 复合条件查询占比：包含2个以上维度的查询，目标≥40%
- 查询类型分布：20个维度的查询均衡度（服装）
- 自主探索率：用户主动尝试新查询类型的比例，目标≥30%

新的 event_type: housekeeper:content_module_params 
应记录助手类型、模块名、模块参数，其中的filters包括了 category、dimenstion keys 等。
至于20个维度的分布，可画为柱状图或热力图。

3. 内容价值指标
内容满意度：
- 点赞率：AI回答获得点赞的比例，目标≥80%
前端事件：点赞

- 点踩率：目标≤5%
同上

- 重新回答率：用户要求重新回答的比例，目标≤10%
同上

内容利用：
前端任务：需要统计上报
- 「文档」导出率：查询结果导出频次/总查询次数，目标≥30%（说明用于实际工作）
- 「文档」分享率：团队内分享文档的频次，目标≥15%
- 「文档」收藏率：有价值内容收藏比例，目标≥20%
- 「聊天」分享率：团队内分享对话的频次，目标≥15%
  
推送效果：
前端任务：需要统计上报
- 「洞察」会话点击率：那个会话的点击概率，等于当日点击过该会话的独立用户数 / 日活数
- 「洞察」点击率：AI推送信息的打开率，目标≥40%
- 「洞察」收藏率：推送内容被收藏的比例，目标≥25%
  
用户留存：
- D1/D7/D30留存率：目标50%/30%/70%（B2B产品周末低活跃正常）
上网搜索，有一些SQL可以生成的。

- 回访间隔：用户两次使用间隔时间，目标≤2天
同上



4. 商业价值指标
决策影响：
- 高价值查询占比：涉及具体商业决策的查询比例，目标≥60%
  - 如："近期什么颜色的羽绒服卖得好？"
  - 如："我该进多少件印花连衣裙？"
人工看吧


- 报告生成频次：基于系统数据生成的业务报告数量，目标≥4次/月
忽略本需求，之后再讨论

- 工作时间使用率：工作日9-18点的使用占比，目标≥75%
分别统计housekeeper:query 和 任意事件，画出热力图。



团队渗透：
- 多人使用率：同一企业内多人使用的比例，目标≥3人
- 跨部门使用：产品、运营、市场等不同部门都有使用，目标≥2个部门
- 管理层使用率：总监级以上用户的活跃率，目标≥50%
  
使用userAgent + ip + userid识别不同的设备，先看看一个user id在同一天内，在几个独立设备、独立IP上使用了。