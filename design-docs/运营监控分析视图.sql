-- 运营监控分析视图
-- 根据需求文档创建的各种分析视图，用于 Superset 可视化

-- =============================================================================
-- 1. 使用粘性分析
-- =============================================================================

-- 1.1 日活跃度分析视图
-- 目标：每日使用用户/总开通用户 ≥ 40%
CREATE VIEW v_daily_activity_rate AS
SELECT 
    date,
    daily_active_users,
    total_whitelist_users,
    ROUND(daily_active_users * 100.0 / NULLIF(total_whitelist_users, 0), 2) as activity_rate_percent,
    CASE 
        WHEN daily_active_users * 100.0 / NULLIF(total_whitelist_users, 0) >= 40 THEN '达标'
        ELSE '未达标'
    END as target_status
FROM (
    SELECT 
        toDate(event_time) as date,
        uniq(user_id) as daily_active_users,
        (
            SELECT count(DISTINCT phone) 
            FROM datawarehouse.users_whitelist 
            WHERE is_internal_user = false
        ) as total_whitelist_users
    FROM datawarehouse.ods_user_activity_events e
    LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
    WHERE toDate(event_time) >= today() - 30
      AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
    GROUP BY date
) t
ORDER BY date;

-- 1.2 平均日查询次数分析视图
-- 目标：≥8次/用户/天
CREATE VIEW v_daily_query_average AS
SELECT 
    date,
    assistant_type,
    total_queries,
    active_users,
    ROUND(total_queries * 1.0 / NULLIF(active_users, 0), 2) as avg_queries_per_user,
    CASE 
        WHEN total_queries * 1.0 / NULLIF(active_users, 0) >= 8 THEN '达标'
        ELSE '未达标'
    END as target_status
FROM (
    SELECT 
        toDate(event_time) as date,
        JSON_EXTRACT(event_args, '$.assistant_type') as assistant_type,
        count(*) as total_queries,
        uniq(user_id) as active_users
    FROM datawarehouse.ods_user_activity_events e
    LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
    WHERE event_name = 'housekeeper:query'
      AND toDate(event_time) >= today() - 30
      AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
      AND JSON_EXTRACT(event_args, '$.assistant_type') IN ('ecommerce', 'content')
    GROUP BY date, assistant_type
) t
ORDER BY date, assistant_type;

-- 1.3 高频用户占比分析视图
-- 目标：每日查询≥15次的用户比例 ≥ 20%
CREATE VIEW v_high_frequency_users AS
SELECT 
    date,
    total_users,
    high_freq_users,
    ROUND(high_freq_users * 100.0 / NULLIF(total_users, 0), 2) as high_freq_rate_percent,
    CASE 
        WHEN high_freq_users * 100.0 / NULLIF(total_users, 0) >= 20 THEN '达标'
        ELSE '未达标'
    END as target_status
FROM (
    SELECT 
        date,
        count(*) as total_users,
        countIf(daily_queries >= 15) as high_freq_users
    FROM (
        SELECT 
            toDate(event_time) as date,
            user_id,
            count(*) as daily_queries
        FROM datawarehouse.ods_user_activity_events e
        LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
        WHERE event_name = 'housekeeper:query'
          AND toDate(event_time) >= today() - 30
          AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
        GROUP BY date, user_id
    ) user_daily_queries
    GROUP BY date
) t
ORDER BY date;

-- 1.4 连续活跃天数分析视图
-- 目标：用户最长连续使用天数 ≥ 5天
CREATE VIEW v_consecutive_active_days AS
WITH user_active_dates AS (
    SELECT DISTINCT
        user_id,
        toDate(event_time) as active_date
    FROM datawarehouse.ods_user_activity_events e
    LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
    WHERE toDate(event_time) >= today() - 90
      AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
),
user_consecutive_days AS (
    SELECT 
        user_id,
        active_date,
        active_date - toDate(row_number() OVER (PARTITION BY user_id ORDER BY active_date)) as group_date
    FROM user_active_dates
),
user_max_consecutive AS (
    SELECT 
        user_id,
        max(count(*)) as max_consecutive_days
    FROM user_consecutive_days
    GROUP BY user_id, group_date
    GROUP BY user_id
)
SELECT 
    max_consecutive_days,
    count(*) as user_count,
    ROUND(count(*) * 100.0 / (SELECT count(*) FROM user_max_consecutive), 2) as percentage,
    CASE 
        WHEN max_consecutive_days >= 5 THEN '达标'
        ELSE '未达标'
    END as target_status
FROM user_max_consecutive
GROUP BY max_consecutive_days
ORDER BY max_consecutive_days;

-- 1.5 周活跃率分析视图
-- 目标：每周至少使用3次的用户占比 ≥ 70%
CREATE VIEW v_weekly_activity_rate AS
SELECT 
    week_start,
    total_users,
    active_3plus_users,
    ROUND(active_3plus_users * 100.0 / NULLIF(total_users, 0), 2) as weekly_active_rate_percent,
    CASE 
        WHEN active_3plus_users * 100.0 / NULLIF(total_users, 0) >= 70 THEN '达标'
        ELSE '未达标'
    END as target_status
FROM (
    SELECT 
        week_start,
        count(*) as total_users,
        countIf(weekly_queries >= 3) as active_3plus_users
    FROM (
        SELECT 
            toMonday(toDate(event_time)) as week_start,
            user_id,
            count(*) as weekly_queries
        FROM datawarehouse.ods_user_activity_events e
        LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
        WHERE toDate(event_time) >= today() - 30
          AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
        GROUP BY week_start, user_id
    ) user_weekly_queries
    GROUP BY week_start
) t
ORDER BY week_start;

-- =============================================================================
-- 2. 使用深度分析
-- =============================================================================

-- 2.1 平均会话轮数分析视图
-- 目标：每次对话的问答轮数 ≥ 3轮
CREATE VIEW v_conversation_rounds AS
SELECT 
    date,
    avg_rounds_per_conversation,
    total_conversations,
    CASE 
        WHEN avg_rounds_per_conversation >= 3 THEN '达标'
        ELSE '未达标'
    END as target_status
FROM (
    SELECT 
        toDate(event_time) as date,
        ROUND(avg(toInt32(JSON_EXTRACT(event_args, '$.history_message_count')) + 1), 2) as avg_rounds_per_conversation,
        uniq(JSON_EXTRACT(event_args, '$.conversation_id')) as total_conversations
    FROM datawarehouse.ods_user_activity_events e
    LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
    WHERE event_name = 'housekeeper:query'
      AND toDate(event_time) >= today() - 30
      AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
    GROUP BY date
) t
ORDER BY date;

-- 2.2 会话详情表格视图（用于会话时长分析）
CREATE VIEW v_conversation_details AS
SELECT 
    w.customer_name,
    toDateTime(e.event_time) as event_time,
    JSON_EXTRACT(e.event_args, '$.conversation_id') as conversation_id,
    JSON_EXTRACT(e.event_args, '$.message_content') as message_content,
    toInt32(JSON_EXTRACT(e.event_args, '$.history_message_count')) as history_message_count,
    JSON_EXTRACT(e.event_args, '$.assistant_type') as assistant_type
FROM datawarehouse.ods_user_activity_events e
LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
WHERE event_name = 'housekeeper:query'
  AND toDate(event_time) >= today() - 7
  AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
ORDER BY event_time DESC;

-- 2.3 追问率分析视图
-- 目标：用户在AI回答后继续追问的比例 ≥ 60%
CREATE VIEW v_follow_up_rate AS
SELECT 
    date,
    total_queries,
    follow_up_queries,
    ROUND(follow_up_queries * 100.0 / NULLIF(total_queries, 0), 2) as follow_up_rate_percent,
    CASE 
        WHEN follow_up_queries * 100.0 / NULLIF(total_queries, 0) >= 60 THEN '达标'
        ELSE '未达标'
    END as target_status
FROM (
    SELECT 
        toDate(event_time) as date,
        count(*) as total_queries,
        countIf(toInt32(JSON_EXTRACT(event_args, '$.history_message_count')) > 0) as follow_up_queries
    FROM datawarehouse.ods_user_activity_events e
    LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
    WHERE event_name = 'housekeeper:query'
      AND toDate(event_time) >= today() - 30
      AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
    GROUP BY date
) t
ORDER BY date;
