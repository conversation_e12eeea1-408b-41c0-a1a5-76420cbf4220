-- 运营监控分析视图
-- 根据需求文档创建的各种分析视图，用于 Superset 可视化

-- =============================================================================
-- 1. 使用粘性分析
-- =============================================================================

-- 1.1 日活跃度分析视图
-- 目标：每日使用用户/总开通用户 ≥ 40%
CREATE VIEW v_daily_activity_rate AS
SELECT 
    date,
    daily_active_users,
    total_whitelist_users,
    ROUND(daily_active_users * 100.0 / NULLIF(total_whitelist_users, 0), 2) as activity_rate_percent,
    CASE 
        WHEN daily_active_users * 100.0 / NULLIF(total_whitelist_users, 0) >= 40 THEN '达标'
        ELSE '未达标'
    END as target_status
FROM (
    SELECT 
        toDate(event_time) as date,
        uniq(user_id) as daily_active_users,
        (
            SELECT count(DISTINCT phone) 
            FROM datawarehouse.users_whitelist 
            WHERE is_internal_user = false
        ) as total_whitelist_users
    FROM datawarehouse.ods_user_activity_events e
    LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
    WHERE toDate(event_time) >= today() - 30
      AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
    GROUP BY date
) t
ORDER BY date;

-- 1.2 平均日查询次数分析视图
-- 目标：≥8次/用户/天
CREATE VIEW v_daily_query_average AS
SELECT 
    date,
    assistant_type,
    total_queries,
    active_users,
    ROUND(total_queries * 1.0 / NULLIF(active_users, 0), 2) as avg_queries_per_user,
    CASE 
        WHEN total_queries * 1.0 / NULLIF(active_users, 0) >= 8 THEN '达标'
        ELSE '未达标'
    END as target_status
FROM (
    SELECT 
        toDate(event_time) as date,
        JSON_EXTRACT(event_args, '$.assistant_type') as assistant_type,
        count(*) as total_queries,
        uniq(user_id) as active_users
    FROM datawarehouse.ods_user_activity_events e
    LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
    WHERE event_name = 'housekeeper:query'
      AND toDate(event_time) >= today() - 30
      AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
      AND JSON_EXTRACT(event_args, '$.assistant_type') IN ('ecommerce', 'content')
    GROUP BY date, assistant_type
) t
ORDER BY date, assistant_type;

-- 1.3 高频用户占比分析视图
-- 目标：每日查询≥15次的用户比例 ≥ 20%
CREATE VIEW v_high_frequency_users AS
SELECT 
    date,
    total_users,
    high_freq_users,
    ROUND(high_freq_users * 100.0 / NULLIF(total_users, 0), 2) as high_freq_rate_percent,
    CASE 
        WHEN high_freq_users * 100.0 / NULLIF(total_users, 0) >= 20 THEN '达标'
        ELSE '未达标'
    END as target_status
FROM (
    SELECT 
        date,
        count(*) as total_users,
        countIf(daily_queries >= 15) as high_freq_users
    FROM (
        SELECT 
            toDate(event_time) as date,
            user_id,
            count(*) as daily_queries
        FROM datawarehouse.ods_user_activity_events e
        LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
        WHERE event_name = 'housekeeper:query'
          AND toDate(event_time) >= today() - 30
          AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
        GROUP BY date, user_id
    ) user_daily_queries
    GROUP BY date
) t
ORDER BY date;

-- 1.4 连续活跃天数分析视图
-- 目标：用户最长连续使用天数 ≥ 5天
CREATE VIEW v_consecutive_active_days AS
WITH user_active_dates AS (
    SELECT DISTINCT
        user_id,
        toDate(event_time) as active_date
    FROM datawarehouse.ods_user_activity_events e
    LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
    WHERE toDate(event_time) >= today() - 90
      AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
),
user_consecutive_days AS (
    SELECT 
        user_id,
        active_date,
        active_date - toDate(row_number() OVER (PARTITION BY user_id ORDER BY active_date)) as group_date
    FROM user_active_dates
),
user_max_consecutive AS (
    SELECT 
        user_id,
        max(count(*)) as max_consecutive_days
    FROM user_consecutive_days
    GROUP BY user_id, group_date
    GROUP BY user_id
)
SELECT 
    max_consecutive_days,
    count(*) as user_count,
    ROUND(count(*) * 100.0 / (SELECT count(*) FROM user_max_consecutive), 2) as percentage,
    CASE 
        WHEN max_consecutive_days >= 5 THEN '达标'
        ELSE '未达标'
    END as target_status
FROM user_max_consecutive
GROUP BY max_consecutive_days
ORDER BY max_consecutive_days;

-- 1.5 周活跃率分析视图
-- 目标：每周至少使用3次的用户占比 ≥ 70%
CREATE VIEW v_weekly_activity_rate AS
SELECT 
    week_start,
    total_users,
    active_3plus_users,
    ROUND(active_3plus_users * 100.0 / NULLIF(total_users, 0), 2) as weekly_active_rate_percent,
    CASE 
        WHEN active_3plus_users * 100.0 / NULLIF(total_users, 0) >= 70 THEN '达标'
        ELSE '未达标'
    END as target_status
FROM (
    SELECT 
        week_start,
        count(*) as total_users,
        countIf(weekly_queries >= 3) as active_3plus_users
    FROM (
        SELECT 
            toMonday(toDate(event_time)) as week_start,
            user_id,
            count(*) as weekly_queries
        FROM datawarehouse.ods_user_activity_events e
        LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
        WHERE toDate(event_time) >= today() - 30
          AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
        GROUP BY week_start, user_id
    ) user_weekly_queries
    GROUP BY week_start
) t
ORDER BY week_start;

-- =============================================================================
-- 2. 使用深度分析
-- =============================================================================

-- 2.1 平均会话轮数分析视图
-- 目标：每次对话的问答轮数 ≥ 3轮
CREATE VIEW v_conversation_rounds AS
SELECT 
    date,
    avg_rounds_per_conversation,
    total_conversations,
    CASE 
        WHEN avg_rounds_per_conversation >= 3 THEN '达标'
        ELSE '未达标'
    END as target_status
FROM (
    SELECT 
        toDate(event_time) as date,
        ROUND(avg(toInt32(JSON_EXTRACT(event_args, '$.history_message_count')) + 1), 2) as avg_rounds_per_conversation,
        uniq(JSON_EXTRACT(event_args, '$.conversation_id')) as total_conversations
    FROM datawarehouse.ods_user_activity_events e
    LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
    WHERE event_name = 'housekeeper:query'
      AND toDate(event_time) >= today() - 30
      AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
    GROUP BY date
) t
ORDER BY date;

-- 2.2 会话详情表格视图（用于会话时长分析）
CREATE VIEW v_conversation_details AS
SELECT 
    w.customer_name,
    toDateTime(e.event_time) as event_time,
    JSON_EXTRACT(e.event_args, '$.conversation_id') as conversation_id,
    JSON_EXTRACT(e.event_args, '$.message_content') as message_content,
    toInt32(JSON_EXTRACT(e.event_args, '$.history_message_count')) as history_message_count,
    JSON_EXTRACT(e.event_args, '$.assistant_type') as assistant_type
FROM datawarehouse.ods_user_activity_events e
LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
WHERE event_name = 'housekeeper:query'
  AND toDate(event_time) >= today() - 7
  AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
ORDER BY event_time DESC;

-- 2.3 追问率分析视图
-- 目标：用户在AI回答后继续追问的比例 ≥ 60%
CREATE VIEW v_follow_up_rate AS
SELECT 
    date,
    total_queries,
    follow_up_queries,
    ROUND(follow_up_queries * 100.0 / NULLIF(total_queries, 0), 2) as follow_up_rate_percent,
    CASE 
        WHEN follow_up_queries * 100.0 / NULLIF(total_queries, 0) >= 60 THEN '达标'
        ELSE '未达标'
    END as target_status
FROM (
    SELECT 
        toDate(event_time) as date,
        count(*) as total_queries,
        countIf(toInt32(JSON_EXTRACT(event_args, '$.history_message_count')) > 0) as follow_up_queries
    FROM datawarehouse.ods_user_activity_events e
    LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
    WHERE event_name = 'housekeeper:query'
      AND toDate(event_time) >= today() - 30
      AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
    GROUP BY date
) t
ORDER BY date;

-- 2.4 查询复杂度分析视图
-- 目标：包含2个以上维度的查询 ≥ 40%
CREATE VIEW v_query_complexity AS
SELECT
    date,
    total_queries,
    complex_queries,
    ROUND(complex_queries * 100.0 / NULLIF(total_queries, 0), 2) as complex_query_rate_percent,
    CASE
        WHEN complex_queries * 100.0 / NULLIF(total_queries, 0) >= 40 THEN '达标'
        ELSE '未达标'
    END as target_status
FROM (
    SELECT
        toDate(event_time) as date,
        count(*) as total_queries,
        countIf(
            length(splitByString(',', JSON_EXTRACT(event_args, '$.module_params.filters'))) >= 2
            OR length(splitByString(',', JSON_EXTRACT(event_args, '$.module_params.dimensions'))) >= 2
        ) as complex_queries
    FROM datawarehouse.ods_user_activity_events e
    LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
    WHERE event_name = 'housekeeper:content_module_params'
      AND toDate(event_time) >= today() - 30
      AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
    GROUP BY date
) t
ORDER BY date;

-- 2.5 查询类型分布视图（20个维度的查询均衡度）
CREATE VIEW v_query_dimension_distribution AS
SELECT
    dimension_key,
    query_count,
    ROUND(query_count * 100.0 / total_queries, 2) as percentage
FROM (
    SELECT
        arrayJoin(JSONExtractKeys(JSON_EXTRACT(event_args, '$.module_params.filters'))) as dimension_key,
        count(*) as query_count,
        (SELECT count(*) FROM datawarehouse.ods_user_activity_events
         WHERE event_name = 'housekeeper:content_module_params'
         AND toDate(event_time) >= today() - 30) as total_queries
    FROM datawarehouse.ods_user_activity_events e
    LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
    WHERE event_name = 'housekeeper:content_module_params'
      AND toDate(event_time) >= today() - 30
      AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
    GROUP BY dimension_key
) t
ORDER BY query_count DESC;

-- =============================================================================
-- 3. 内容价值指标分析
-- =============================================================================

-- 3.1 用户留存率分析视图
-- 目标：D1/D7/D30留存率 50%/30%/70%
CREATE VIEW v_user_retention AS
WITH first_activity AS (
    SELECT
        user_id,
        min(toDate(event_time)) as first_active_date
    FROM datawarehouse.ods_user_activity_events e
    LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
    WHERE (w.is_internal_user = false OR w.is_internal_user IS NULL)
    GROUP BY user_id
),
retention_data AS (
    SELECT
        f.first_active_date,
        f.user_id,
        -- D1留存
        CASE WHEN EXISTS(
            SELECT 1 FROM datawarehouse.ods_user_activity_events e2
            WHERE e2.user_id = f.user_id
            AND toDate(e2.event_time) = f.first_active_date + 1
        ) THEN 1 ELSE 0 END as d1_retained,
        -- D7留存
        CASE WHEN EXISTS(
            SELECT 1 FROM datawarehouse.ods_user_activity_events e2
            WHERE e2.user_id = f.user_id
            AND toDate(e2.event_time) = f.first_active_date + 7
        ) THEN 1 ELSE 0 END as d7_retained,
        -- D30留存
        CASE WHEN EXISTS(
            SELECT 1 FROM datawarehouse.ods_user_activity_events e2
            WHERE e2.user_id = f.user_id
            AND toDate(e2.event_time) = f.first_active_date + 30
        ) THEN 1 ELSE 0 END as d30_retained
    FROM first_activity f
    WHERE f.first_active_date >= today() - 60
)
SELECT
    first_active_date,
    count(*) as new_users,
    ROUND(avg(d1_retained) * 100, 2) as d1_retention_rate,
    ROUND(avg(d7_retained) * 100, 2) as d7_retention_rate,
    ROUND(avg(d30_retained) * 100, 2) as d30_retention_rate
FROM retention_data
GROUP BY first_active_date
ORDER BY first_active_date;

-- 3.2 工作时间使用率热力图数据视图
-- 目标：工作日9-18点的使用占比 ≥ 75%
CREATE VIEW v_work_time_usage_heatmap AS
SELECT
    toHour(event_time) as hour,
    toDayOfWeek(toDate(event_time)) as day_of_week,
    count(*) as event_count,
    CASE
        WHEN toDayOfWeek(toDate(event_time)) IN (1,7) THEN '周末'
        WHEN toHour(event_time) BETWEEN 9 AND 18 THEN '工作时间'
        ELSE '非工作时间'
    END as time_category
FROM datawarehouse.ods_user_activity_events e
LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
WHERE event_name = 'housekeeper:query'
  AND toDate(event_time) >= today() - 30
  AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
GROUP BY hour, day_of_week
ORDER BY day_of_week, hour;

-- 3.3 工作时间使用率统计视图
CREATE VIEW v_work_time_usage_stats AS
SELECT
    date,
    total_queries,
    work_time_queries,
    ROUND(work_time_queries * 100.0 / NULLIF(total_queries, 0), 2) as work_time_rate_percent,
    CASE
        WHEN work_time_queries * 100.0 / NULLIF(total_queries, 0) >= 75 THEN '达标'
        ELSE '未达标'
    END as target_status
FROM (
    SELECT
        toDate(event_time) as date,
        count(*) as total_queries,
        countIf(
            toDayOfWeek(toDate(event_time)) NOT IN (1,7)
            AND toHour(event_time) BETWEEN 9 AND 18
        ) as work_time_queries
    FROM datawarehouse.ods_user_activity_events e
    LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
    WHERE event_name = 'housekeeper:query'
      AND toDate(event_time) >= today() - 30
      AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
    GROUP BY date
) t
ORDER BY date;

-- =============================================================================
-- 4. 商业价值指标分析
-- =============================================================================

-- 4.1 团队渗透率分析视图
-- 目标：同一企业内多人使用的比例 ≥ 3人
CREATE VIEW v_team_penetration AS
WITH user_device_stats AS (
    SELECT
        user_id,
        toDate(event_time) as date,
        uniq(client_ip) as unique_ips,
        uniq(user_agent) as unique_devices
    FROM datawarehouse.ods_user_activity_events e
    LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
    WHERE toDate(event_time) >= today() - 30
      AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
    GROUP BY user_id, date
),
company_usage AS (
    SELECT
        w.customer_name as company_name,
        toDate(e.event_time) as date,
        uniq(e.user_id) as active_users_count
    FROM datawarehouse.ods_user_activity_events e
    LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
    WHERE toDate(event_time) >= today() - 30
      AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
      AND w.customer_name IS NOT NULL
    GROUP BY company_name, date
)
SELECT
    date,
    count(*) as total_companies,
    countIf(active_users_count >= 3) as multi_user_companies,
    ROUND(countIf(active_users_count >= 3) * 100.0 / count(*), 2) as multi_user_rate_percent,
    CASE
        WHEN countIf(active_users_count >= 3) * 100.0 / count(*) >= 50 THEN '达标'
        ELSE '未达标'
    END as target_status
FROM company_usage
GROUP BY date
ORDER BY date;

-- 4.2 设备使用分析视图
-- 分析用户在不同设备和IP上的使用情况
CREATE VIEW v_device_usage_analysis AS
SELECT
    date,
    user_id,
    unique_ips,
    unique_devices,
    CASE
        WHEN unique_ips >= 3 OR unique_devices >= 3 THEN '多设备用户'
        WHEN unique_ips = 2 OR unique_devices = 2 THEN '双设备用户'
        ELSE '单设备用户'
    END as device_category
FROM (
    SELECT
        user_id,
        toDate(event_time) as date,
        uniq(client_ip) as unique_ips,
        uniq(user_agent) as unique_devices
    FROM datawarehouse.ods_user_activity_events e
    LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
    WHERE toDate(event_time) >= today() - 30
      AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
    GROUP BY user_id, date
) t
ORDER BY date, user_id;
