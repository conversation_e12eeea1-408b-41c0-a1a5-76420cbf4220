# 运营监控实施指南

## 概述

本文档提供了基于用户活动监控系统的运营监控实施指南，包括数据分析视图的使用方法和 Superset 可视化配置建议。

## 已实现的后端事件收集

### 1. housekeeper:query 事件
**收集位置**: `HousekeeperProcessor._do_reply()`
**事件参数**:
- `assistant_type`: 助手类型（ecommerce, content等）
- `message_content`: 用户发送的消息内容
- `conversation_id`: 会话ID
- `user_message_count`: 用户在当前会话中发送的消息总数（完整统计）
- `total_message_count`: 会话中的总消息数量（包括用户和助手消息）
- `history_message_count`: 历史消息长度（向后兼容字段）

**消息统计特性**:
- 获取会话中的所有消息进行统计（不限于最近10条）
- 自动过滤掉清除上下文之前的消息
- 异常时降级使用当前可用消息

### 2. housekeeper:content_module_params 事件
**收集位置**: `HousekeeperAgentBase._do_module_call()`
**事件参数**:
- `assistant_type`: 助手类型
- `module_name`: 内容模块名称
- `module_params`: 模块参数
  - `filters`: 过滤器信息（category、brand等）
  - `dimensions`: 维度信息
  - `limit`: 限制数量
  - `sns_platform`: 数据平台

## 数据分析视图说明

### 1. 使用粘性分析

#### 1.1 日活跃度分析 (`v_daily_activity_rate`)
- **目标**: ≥ 40%
- **指标**: 每日使用用户/总开通用户
- **Superset图表**: 折线图，显示活跃度趋势和达标状态

#### 1.2 平均日查询次数 (`v_daily_query_average`)
- **目标**: ≥ 8次/用户/天
- **指标**: 按助手类型分组的平均查询次数
- **Superset图表**: 分组柱状图，按助手类型显示

#### 1.3 高频用户占比 (`v_high_frequency_users`)
- **目标**: ≥ 20%
- **指标**: 每日查询≥15次的用户比例
- **Superset图表**: 折线图 + 目标线

#### 1.4 连续活跃天数 (`v_consecutive_active_days`)
- **目标**: ≥ 5天
- **指标**: 用户最长连续使用天数分布
- **Superset图表**: 柱状图，显示不同连续天数的用户分布

#### 1.5 周活跃率 (`v_weekly_activity_rate`)
- **目标**: ≥ 70%
- **指标**: 每周至少使用3次的用户占比
- **Superset图表**: 折线图，按周显示

### 2. 使用深度分析

#### 2.1 平均会话轮数 (`v_conversation_rounds`)
- **目标**: ≥ 3轮
- **指标**: 每次对话的用户消息数和总消息数
- **新增字段**:
  - `avg_user_messages_per_conversation`: 平均用户消息数
  - `avg_total_messages_per_conversation`: 平均总消息数
- **Superset图表**: 双线折线图，显示用户消息数和总消息数趋势

#### 2.2 会话详情表格 (`v_conversation_details`)
- **用途**: 会话时长分析的基础数据
- **新增字段**:
  - `user_message_count`: 用户消息总数
  - `total_message_count`: 总消息数量
- **Superset图表**: 表格，支持筛选和排序

#### 2.3 追问率分析 (`v_follow_up_rate`)
- **目标**: ≥ 60%
- **指标**: 用户在AI回答后继续追问的比例
- **新增字段**:
  - `follow_up_rate_percent`: 基于用户消息数的追问率
  - `multi_round_rate_percent`: 基于总消息数的多轮对话率
- **Superset图表**: 双线折线图，显示两种追问率指标

#### 2.4 查询复杂度 (`v_query_complexity`)
- **目标**: ≥ 40%
- **指标**: 包含2个以上维度的查询比例
- **Superset图表**: 折线图 + 目标线

#### 2.5 查询类型分布 (`v_query_dimension_distribution`)
- **用途**: 20个维度的查询均衡度分析
- **Superset图表**: 饼图或柱状图，显示各维度使用频率

### 3. 内容价值指标分析

#### 3.1 用户留存率 (`v_user_retention`)
- **目标**: D1/D7/D30留存率 50%/30%/70%
- **指标**: 新用户的留存情况
- **Superset图表**: 多线折线图，显示D1/D7/D30留存率

#### 3.2 工作时间使用率热力图 (`v_work_time_usage_heatmap`)
- **目标**: ≥ 75%
- **指标**: 工作日9-18点的使用分布
- **Superset图表**: 热力图，X轴为小时，Y轴为星期

#### 3.3 工作时间使用率统计 (`v_work_time_usage_stats`)
- **目标**: ≥ 75%
- **指标**: 工作时间使用占比
- **Superset图表**: 折线图 + 目标线

### 4. 商业价值指标分析

#### 4.1 团队渗透率 (`v_team_penetration`)
- **目标**: ≥ 3人/企业
- **指标**: 同一企业内多人使用的比例
- **Superset图表**: 折线图，显示多用户企业占比

#### 4.2 设备使用分析 (`v_device_usage_analysis`)
- **用途**: 分析用户设备使用模式
- **Superset图表**: 饼图，显示单设备/双设备/多设备用户分布

## Superset 仪表板配置建议

### 仪表板1: 使用粘性概览
- 日活跃度趋势图
- 平均查询次数（按助手类型）
- 高频用户占比
- 周活跃率

### 仪表板2: 使用深度分析
- 平均会话轮数
- 追问率趋势
- 查询复杂度分析
- 查询类型分布（饼图）

### 仪表板3: 用户留存与时间分析
- 用户留存率（D1/D7/D30）
- 工作时间使用热力图
- 连续活跃天数分布

### 仪表板4: 商业价值指标
- 团队渗透率
- 设备使用分析
- 工作时间使用率统计

## 实施步骤

### 1. 数据库准备
```sql
-- 在 ClickHouse 中执行所有视图创建语句
-- 文件: design-docs/运营监控分析视图.sql
```

### 2. Superset 配置
1. 连接 ClickHouse 数据源
2. 添加所有分析视图作为数据集
3. 创建图表和仪表板
4. 设置自动刷新和告警

### 3. 监控告警设置
- 日活跃度 < 40% 时告警
- 平均查询次数 < 8次 时告警
- 追问率 < 60% 时告警
- 工作时间使用率 < 75% 时告警

### 4. 定期分析报告
- 每日: 活跃度、查询次数
- 每周: 留存率、使用深度
- 每月: 团队渗透、商业价值指标

## 注意事项

1. **数据隐私**: 确保敏感信息已脱敏
2. **性能优化**: 大数据量时考虑分区和索引
3. **数据质量**: 定期检查事件收集的完整性
4. **业务理解**: 结合业务场景解读数据指标

## 扩展建议

1. **实时监控**: 集成流处理引擎支持实时分析
2. **用户画像**: 基于行为数据构建用户标签
3. **预测分析**: 使用机器学习预测用户流失
4. **A/B测试**: 支持功能效果对比分析
