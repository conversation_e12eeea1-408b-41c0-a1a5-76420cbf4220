# 用户活动监控系统设计文档

## 概述

用户活动监控系统用于收集、存储和分析用户在应用中的各种行为事件，为产品优化和用户体验改进提供数据支持。

## 系统架构

### 核心组件

1. **事件收集器** (`app/service/activity/collector.py`)
   - 统一的事件收集入口
   - 事件类型验证
   - 自动添加时间戳和上下文信息

2. **事件类型注册** (`app/service/activity/events.py`)
   - 定义所有支持的事件类型
   - 事件类型验证器
   - 前端/后端事件分类

3. **活动路由** (`app/routers/activity.py`)
   - 前端事件收集API
   - 用户身份验证
   - 请求参数验证

4. **用户白名单同步** (`app/tasks/sync_feishu_whitelist.py`)
   - 从飞书表格同步白名单数据
   - 多环境支持（staging + prod）
   - 自动用户ID关联

## 数据模型

### 用户活动事件表

**ClickHouse表**: `datawarehouse.ods_user_activity_events`

```sql
CREATE TABLE datawarehouse.ods_user_activity_events (
    event_time DateTime,                 -- 事件发生时间，用于按日分区
    event_name String,                   -- 事件名称（如：SEND_MESSAGE, EXPORT_PDF等）
    user_id String,                      -- 用户ID
    source String,                       -- 事件来源：web | backend
    event_args String,                   -- 事件参数JSON
    user_agent String,                   -- 用户代理信息
    client_ip String                     -- 客户端IP地址
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(event_time)
ORDER BY (event_name, user_id, event_time);
```

### 用户白名单表

**ClickHouse表**: `datawarehouse.users_whitelist`

```sql
CREATE TABLE datawarehouse.users_whitelist (
    phone String,                        -- 手机号（用户标识）
    customer_name String,                -- 客户名称（看板显示用）
    env_source String,                   -- 数据来源环境：staging | prod
    user_id Nullable(UInt32),            -- 用户ID，通过手机号查询获得
    is_internal_user Boolean,            -- 是否内部人员（true=是，统计时排除）
    additional String,                   -- 扩展信息JSON: {"added_by": "张三", "ecommerce_category": "服装", "content_category": "美妆", "reading_category": "小说"}
    created_at DateTime DEFAULT now(),
    updated_at DateTime DEFAULT now()
) ENGINE = ReplacingMergeTree(updated_at)
ORDER BY (env_source, phone);
```

## 支持的事件类型

### 登录相关
- `SEND_CODE`: 发送验证码
- `SUBMIT_LOGIN`: 提交登录

### 会话相关
- `CREATE_CONVERSATION`: 创建会话
- `DELETE_CONVERSATION`: 删除会话
- `TOP_CONVERSATION`: 置顶会话
- `SELECT_CONVERSATION`: 会话点击

### 会话记录相关
- `SEND_MESSAGE`: 发送消息（核心查询事件）
- `COMMAND_MESSAGE`: 发送命令行消息
- `CLEAR_CONTEXT`: 清除上下文
- `VISIT_HISTORY`: 访问会话历史
- `SHARE_MESSAGE`: 分享消息记录

### 文档相关
- `CREATE_DOCUMENT`: 创建文档
- `RENAME_DOCUMENT`: 重命名文档
- `TRASH_DOCUMENT`: 删除文档
- `RECOVER_DOCUMENT`: 恢复文档
- `DELETE_DOCUMENT`: 彻底删除文档
- `SHARE_DOCUMENT`: 分享文档
- `VISIT_DOCUMENT`: 访问文档
- `LEAVE_DOCUMENT`: 离开文档
- `FAVORITE_DOCUMENT`: 收藏文档
- `EXPORT_PDF`: 导出PDF
- `EXPORT_WORD`: 导出Word
- `EXPORT_MARKDOWN`: 导出Markdown

### 搜索和点击相关
- `SUBMIT_SEARCH`: 提交搜索
- `CLICK_VIDEO`: 点击视频
- `CLICK_BIAKE`: 点击流联百科

### 界面操作相关
- `TOGGLE_SIDEBAR`: 切换侧边栏
- `TOGGLE_SPLITEVIEW`: 切换子窗口

## API接口

### 事件收集接口

**POST** `/activity/collect`

```json
{
    "event_name": "SEND_MESSAGE",
    "event_args": {
        "message_length": 100,
        "conversation_id": "12345"
    }
}
```

**响应**:
```json
{
    "code": 200,
    "msg": "success",
    "data": null
}
```

## 使用示例

### 前端事件收集

```javascript
// 发送消息事件
fetch('/activity/collect', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer <token>'
    },
    body: JSON.stringify({
        event_name: 'SEND_MESSAGE',
        event_args: {
            message_length: 150,
            conversation_id: 'conv_12345',
            message_type: 'text'
        }
    })
});
```

### 后端事件收集

```python
from app.service.activity.collector import collect_event

# 助手查询事件
collect_event(
    event_name="housekeeper:query",
    user_id="12345",
    source="backend",
    event_args={
        "query_type": "content_analysis",
        "processing_time_ms": 1500
    }
)
```

## 数据分析查询示例

### 用户活跃度分析

```sql
-- 日活跃用户数（排除内部用户）
SELECT
    toDate(event_time) as date,
    uniq(user_id) as daily_active_users
FROM datawarehouse.ods_user_activity_events e
LEFT JOIN datawarehouse.users_whitelist w ON e.user_id = toString(w.user_id)
WHERE toDate(event_time) >= today() - 30
  AND (w.is_internal_user = false OR w.is_internal_user IS NULL)
GROUP BY date
ORDER BY date;
```

### 核心功能使用统计

```sql
-- 消息发送统计
SELECT
    toDate(event_time) as date,
    count(*) as message_count,
    uniq(user_id) as active_users
FROM datawarehouse.ods_user_activity_events
WHERE event_name = 'SEND_MESSAGE'
  AND toDate(event_time) >= today() - 7
GROUP BY date
ORDER BY date;
```

### 文档操作分析

```sql
-- 文档导出类型分布
SELECT
    event_name,
    count(*) as export_count,
    uniq(user_id) as user_count
FROM datawarehouse.ods_user_activity_events
WHERE event_name IN ('EXPORT_PDF', 'EXPORT_WORD', 'EXPORT_MARKDOWN')
  AND toDate(event_time) >= today() - 30
GROUP BY event_name
ORDER BY export_count DESC;
```

## 白名单管理

### 飞书表格同步

系统自动从飞书表格同步用户白名单数据：

- **同步频率**: 每3分钟检查一次
- **多环境支持**: 分别同步 staging 和 prod 环境数据
- **分环境更新**: staging 只更新 staging 的行，prod 只更新 prod 的行
- **同步策略**: 先删除指定环境的现有数据，再插入新数据
- **智能同步**: 只有数据变更时才执行实际同步
- **用户关联**: 自动通过手机号查询并关联用户ID

### 白名单字段说明

- `phone`: 手机号，主要标识符
- `customer_name`: 客户名称，用于看板显示
- `env_source`: 数据来源环境（staging/prod）
- `user_id`: 系统用户ID，通过手机号自动查询
- `is_internal_user`: 内部用户标识，统计时排除
- `additional`: 扩展信息，包含品类偏好等

## 部署和配置

### 自动启动

系统在应用启动时自动启动白名单后台同步服务：

```python
# app/main.py
@app.on_event("startup")
def es_update_worker():
    # ... 其他初始化代码

    # 启动飞书白名单后台同步服务
    from app.tasks.sync_feishu_whitelist import start_feishu_whitelist_background_sync
    start_feishu_whitelist_background_sync()
    logger.info("飞书白名单后台同步服务已启动")
```

### 监控和日志

- 所有事件收集都有详细日志记录
- 白名单同步状态可通过API查询
- 异常情况自动记录和报警

## 注意事项

1. **数据隐私**: 确保用户敏感信息不被记录在事件参数中
2. **性能考虑**: 事件收集采用异步处理，不影响主业务流程
3. **数据保留**: 建议设置合理的数据保留策略
4. **权限控制**: 只有授权用户可以访问分析数据

## 扩展计划

1. **实时分析**: 集成流处理引擎，支持实时数据分析
2. **用户画像**: 基于行为数据构建用户画像
3. **异常检测**: 自动检测异常用户行为
4. **A/B测试**: 支持功能使用效果对比分析
