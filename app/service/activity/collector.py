"""
用户活动事件收集器
统一的事件收集和存储服务
"""

import json
from datetime import datetime
from typing import Optional, Dict, Any

from app.ch.orm import ChClient
from app.ch.model import OdsUserActivityEvents
from app.service.activity.events import EventValidator
from app.logger import logger


# 全局ClickHouse客户端
ch_client = ChClient()


def collect_event(
    event_name: str,
    user_id: str,
    source: str = "backend",
    event_args: Optional[Dict[str, Any]] = None,
    user_agent: Optional[str] = None,
    client_ip: Optional[str] = None,
) -> None:
    """
    收集用户活动事件

    Args:
        event_name: 事件名称
        user_id: 用户ID
        source: 事件来源，只能是 "web" 或 "backend"
        event_args: 事件参数字典
        user_agent: 用户代理信息
        client_ip: 客户端IP

    Raises:
        ValueError: 当事件类型无效或source参数无效时
        Exception: 当插入数据库失败时
    """
    # 验证事件类型
    if not EventValidator.is_valid_event_type(event_name):
        raise ValueError(f"Invalid event type: {event_name}")

    # 验证source参数
    if source not in ("web", "backend"):
        raise ValueError(f"Invalid source: {source}. Must be 'web' or 'backend'")

    # 设置默认值
    if event_args is None:
        event_args = {}

    # 创建事件记录，使用当前时间作为事件时间
    event_record = OdsUserActivityEvents(
        event_name=event_name,
        user_id=user_id,
        source=source,
        event_args=json.dumps(event_args, ensure_ascii=False),
        user_agent=user_agent or "",
        client_ip=client_ip or "",
        event_time=datetime.now(),
    )

    # 同步插入到ClickHouse数据仓库
    ch_client.dw_db.insert([event_record])

    logger.info("Event collected: %s for user %s", event_name, user_id)
