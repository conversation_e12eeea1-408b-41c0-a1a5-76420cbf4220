import os
import time
import joblib
import traceback
import tempfile
from typing import List

from app.logger import logger
from app.utils.dsl_engine.dsl_engine import (
    DslInfo,
    DslEngine,
    DSLFieldInfo,
    DimensionInfo,
)
from app.service.content_modules.attribution_analysis.model.utils import (
    FilterGroup,
    CategoryInfo,
    get_model_name,
)
from app.service.content_modules.attribution_analysis.model_for_ca import (
    AttributionAnalysisModel,
)
from app.service.content_modules.attribution_analysis.model.upload_model import (
    upload_model,
)


def train_one_model(
    filter_group: FilterGroup,
    local_path: str,
):
    model_name = get_model_name(filter_group)
    log_prefix = f"模型 {model_name} "
    logger.info(f"{log_prefix}开始训练，filter_group = {filter_group}")

    # 训练模型
    start_time = time.time()
    attribution_model = AttributionAnalysisModel()
    model_result = attribution_model.train(filter_group)
    time_elapsed = time.time() - start_time
    logger.info(f"{log_prefix}训练结束，耗时 {time_elapsed:.2f} 秒")

    # 确保有本地保存路径
    if not local_path:
        temp_file = tempfile.NamedTemporaryFile(
            prefix="attribution_model_",
            suffix=".joblib",
            delete=False,
        )
        temp_path = temp_file.name
        temp_file.close()
        local_path = temp_path
    local_path = os.path.abspath(local_path)

    # 保存模型到本地
    try:
        joblib.dump({model_name: model_result}, local_path)
    except Exception as e:
        logger.error(f"{log_prefix}保存到本地失败: {e}\n{traceback.format_exc()}")
        return

    # 上传模型
    environments = ["STAGING"]
    success = upload_model(local_path, ["STAGING"])
    if success:
        logger.info(f"{log_prefix}成功上传至 {environments} 环境")
    else:
        logger.error(f"{log_prefix}上传至 {environments} 环境失败")


def inference_one_model(
    filter_group: FilterGroup,
):
    # 构造 filters
    filters = [
        DSLFieldInfo(field="category1", value=filter_group.category_info.category1),
        DSLFieldInfo(field="category2", value=filter_group.category_info.category2),
        DSLFieldInfo(field="fashion_dress_price", value=["平价"], operator="in"),
    ]

    # 获取视频ID
    video_ids = get_video_ids(filters)

    # 推理模型
    from app.service.content_modules.attribution_analysis import (
        get_attribution_analysis_of_video_profile,
    )

    inference_result = get_attribution_analysis_of_video_profile(
        filters=filters,
        content_ids=video_ids,
        date_range=None,
    )
    print(inference_result)


def get_video_ids(filters: List[DSLFieldInfo]) -> List[str]:
    dsl = DslInfo(
        table_name="video_info",
        dimensions=[DimensionInfo(field_name="video_id")],
        metrics=[DSLFieldInfo(field="like_cnt", function="SUM")],
        filters=filters,
        sort=[DSLFieldInfo(field="like_cnt", order="DESC")],
        limit=1_000_000,
    )

    dsl_engine = DslEngine()
    data = dsl_engine.dsl_to_data(dsl)
    return [row[0] for row in data["result_row_list"]]


def train_and_test_one_model():
    # 测试“时尚-穿搭”品类
    category = CategoryInfo(
        category1="时尚",
        category2="穿搭",
    )

    # 构建 filter_group
    filter_group = FilterGroup(category, None)

    # 训练模型
    train_one_model(filter_group, local_path="")
    time.sleep(5)

    # 推理模型
    inference_one_model(filter_group)


if __name__ == "__main__":
    train_and_test_one_model()
