# 内容助手归因分析模型说明文档

## 模型概述

本文档描述了内容助手（Content Assistant）归因分析模型的特征维度划分方案。该模型基于视频内容数据，分析不同特征对内容表现（如观看量、点赞量、销售量等）的影响程度，为内容创作和优化提供数据驱动的建议。

## 数据来源与处理

### 数据获取
- **主要函数**: `get_contents_data()` 位于 `utils.py:141`
- **数据源**: 三个主要数据表
  - `agg_video_base_info_v2`: 视频基础信息
  - `fact_video_metric_1d`: 日度指标数据  
  - `fact_video_metric_distribution_1d`: 分布数据
- **数据粒度**: 以 video_id 为主键，每个视频一条记录

### 数据预处理
1. **维度数据处理** (`process_dim_tuples()` - `utils.py:181`)
   - 解析 `dim_tuples` 字段，展开为观众画像特征
   - 生成 `dim_city`, `dim_province`, `dim_gender`, `dim_interest`, `dim_age` 等特征

2. **属性数据处理** (`process_attributes()` - `utils.py:224`)  
   - 解析 `attributes` 字段，展开为内容属性特征
   - **固定字段**：`topic`, `hash_tags` (保持原名)
   - **非固定字段**：统一添加 `attribute_` 前缀
   - 处理字符串分割（"、"/"，"分隔符）和缺失值填充

## 特征维度划分方案

基于高内聚低耦合原则，将所有特征划分为以下5个维度：

### 1. 观众画像维度 (Audience Profile)

**维度描述**: 描述观看视频的用户群体特征分布

**特征组**:
- **地理分布**: `dim_province`, `dim_city_level`
- **人口统计**: `dim_gender`, `dim_age`  
- **兴趣偏好**: `dim_interest`
- **其他观众维度**: 所有以 `dim_` 开头的字段


### 2. 内容基础维度 (Content Basics)

**维度描述**: 内容的基础属性、创作背景和平台信息

**特征组**:
- **内容基础信息**
  - `duration`: 视频时长
  - `categories`: 主分类
  - `sub_categories`: 子分类
  - `topic`: 主题（固定字段）
  - `hash_tags`: 话题标签（固定字段）

- **创作者信息**
  - `author_fans_cnt`: 作者粉丝数
  - `rank_type`: 达人等级

- **平台技术信息**
  - `create_date`: 创建日期
  - `time_slot`: 时间段特征（将create_time转换为8个时间段的独热编码）


### 3. 商品与内容属性维度 (Product & Content Attributes)

**维度描述**: 所有从 attributes 字段解析的结构化属性（除固定字段外）

**重要说明**: 根据要求，所有 `attribute_*` 特征都归纳在此维度中

**特征组**:
- **商品属性子组**
  - `attribute_fashion_dress_item_type`: 服装类型（下装、上装、鞋类等）
  - `attribute_fashion_dress_style`: 风格（休闲风、时尚风、性感风等）
  - `attribute_fashion_dress_color`: 颜色（黑色、白色、蓝色等）
  - `attribute_fashion_dress_pattern`: 图案（纯色、条纹、印花等）
  - `attribute_fashion_dress_season`: 季节（春夏秋冬）
  - `attribute_fashion_dress_fabric`: 面料（棉、丝等）
  - `attribute_fashion_dress_price`: 价格档次（平价、中档、高档）

- **营销定位子组**
  - `attribute_fashion_dress_selling_points`: 卖点（舒适、显瘦、百搭等）
  - `attribute_fashion_dress_target_audience`: 目标受众（年轻女性、学生党等）
  - `attribute_fashion_dress_body_shape`: 身材适配（小个子、微胖等）
  - `attribute_fashion_dress_usage_scene`: 使用场景（日常通勤、约会等）

- **内容创作子组**
  - `attribute_fashion_dress_content_type`: 内容类型（穿搭分享、舞蹈、展示等）
  - `attribute_fashion_dress_show_way`: 展示方式（细节特写、动作演示等）
  - `attribute_fashion_dress_video_scene`: 视频场景（室内、户外等）
  - `attribute_fashion_dress_video_highlight`: 视频亮点（视觉冲击、创意视觉等）

- **互动策略子组**
  - `attribute_fashion_dress_interaction_hook`: 互动钩子（购买推荐、提问互动等）
  - `attribute_fashion_dress_topic_tag`: 话题标签
  - `attribute_fashion_dress_influencer_profile`: 达人画像特征


### 4. 商业推广维度 (Business Promotion)

**维度描述**: 内容的商业化程度和变现相关特征

**特征组**:
- **带货特征**
  - `bring_goods`: 带货商品信息
  - `is_bring_goods`: 是否带货（布尔值）
  - `promotion_type`: 推广类型

- **商业表现**
  - `sales_amount`: 销售金额
  - `sales_volume`: 销售数量


### 5. 用户反馈维度 (User Engagement)

**维度描述**: 用户对内容的反馈和互动表现

**特征组**:
- **互动指标**
  - `view_cnt`: 观看数量
  - `favourite_cnt`: 收藏数量
  - `share_cnt`: 分享数量
  - `comment_cnt`: 评论数量

- **用户反馈内容**
  - `hot_comment_words`: 热门评论关键词