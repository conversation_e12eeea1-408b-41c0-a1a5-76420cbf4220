import pandas as pd
from datetime import date, timedelta
from typing import List, Optional, Tuple


from app.logger import logger
from app.ch.orm import ChClient
from app.ch.orm import CategoryInfo
from app.utils.category_data import CategoryDataService
from app.service.content_modules.attribution_analysis.model.utils import (
    DIM_RATIO_PREFIX,
    FilterGroup,
)

# from app.service.content_modules.attribution_analysis.model.utils import (
#     parse_wine_degree,
#     parse_wine_volume,
#     parse_attr_place,
#     convert_to_number,
#     parse_array_string,
# )


def generate_contents_query_sql(
    contents_query_conditions: List[str],
    date_range: Optional[Tuple[date, date]] = None,
    category_info: Optional[CategoryInfo] = None,
    limit: int = -1,
) -> str:
    """
    根据查询条件生成SQL

    Args:
        contents_query_conditions: 内容查询条件，比如内容的品类、属性等，或是内容的ID列表
        date_range: 事件日期范围，如果为None，则默认为过去90天
        category_info: 内容分类信息
        limit: 限制返回的video_id数量，如果为-1，则不限制
    """
    # 查询条件
    contents_query_clause = " AND ".join(contents_query_conditions)

    # 日期范围
    if date_range and all(date_range):
        start_date, end_date = date_range
    else:
        end_date = date.today() - timedelta(days=1)
        start_date = end_date - timedelta(days=90)

    sql = f"""
WITH base_info AS (
    SELECT
        video_id,
        argMax(author_fans_cnt, update_time) as author_fans_cnt,
        argMax(rank_type, update_time) as rank_type,
        argMax(bring_goods, update_time) as bring_goods,
        argMax(promotion_type, update_time) as promotion_type,
        argMax(is_bring_goods, update_time) as is_bring_goods,
        argMax(hot_comment_words, update_time) as hot_comment_words,
        argMax(attributes, update_time) as attributes,
        argMax(audience_profile, update_time) as audience_profile,
        argMax(like_cnt, update_time) as like_cnt,
        argMax(comment_cnt, update_time) as comment_cnt,
        argMax(share_cnt, update_time) as share_cnt,
        argMax(favourite_cnt, update_time) as favourite_cnt,
        argMax(view_cnt, update_time) as view_cnt,
        argMax(sales_amount, update_time) as sales_amount,
        argMax(sales_volume, update_time) as sales_volume,
        argMax(duration, update_time) as duration,
        argMax(create_date, update_time) as create_date,
        argMax(create_time, update_time) as create_time,
        argMax(data_source, update_time) as data_source,
        argMax(data_platform, update_time) as data_platform
    FROM (
        SELECT *
        FROM datawarehouse.agg_video_base_info_v2
        {f"WHERE {contents_query_clause}" if contents_query_clause else ""}
    )
    GROUP BY video_id
    ORDER BY create_time DESC
    {f"LIMIT {limit}" if limit != -1 else ""}
),
averaged_dim_info AS (
    SELECT
        video_id,
        arrayStringConcat(
            groupArray(concat('"', sub_dim_type, '","', sub_dim_id, '",', toString(avg_ratio))),
            '|'
        ) as dim_tuples
    FROM (
        SELECT
            video_id,
            sub_dim_type,
            sub_dim_id,
            avg(ratio) as avg_ratio
        FROM datawarehouse.fact_video_metric_distribution_1d
        WHERE
            video_id IN (SELECT video_id FROM base_info)
            {f"AND event_date BETWEEN '{start_date}' AND '{end_date}'" if date_range else ""}
        GROUP BY video_id, sub_dim_type, sub_dim_id, ratio
    ) AS avg_data
    GROUP BY video_id
)
SELECT
    b.video_id as video_id,
    b.view_cnt as view_cnt,
    b.like_cnt as like_cnt,
    b.favourite_cnt as favourite_cnt,
    b.share_cnt as share_cnt,
    b.comment_cnt as comment_cnt,
    b.sales_amount as sales_amount,
    b.sales_volume as sales_volume,
    coalesce(d.dim_tuples, '') as dim_tuples,
    b.author_fans_cnt as author_fans_cnt,
    b.rank_type as rank_type,
    b.bring_goods as bring_goods,
    b.promotion_type as promotion_type,
    b.is_bring_goods as is_bring_goods,
    b.hot_comment_words as hot_comment_words,
    b.attributes as attributes,
    b.audience_profile as audience_profile,
    b.duration as duration,
    b.create_date as create_date,
    b.create_time as create_time,
    b.data_source as data_source,
    b.data_platform as data_platform
FROM base_info b
LEFT JOIN averaged_dim_info d ON b.video_id = d.video_id
"""
    return sql


def get_contents_data(
    contents_query_conditions: List[str],
    date_range: Optional[Tuple[date, date]] = None,
    category_info: Optional[CategoryInfo] = None,
    limit: int = -1,
) -> pd.DataFrame:
    """
    获取内容数据，并进行处理

    Args:
        contents_query_conditions: 内容查询条件，比如内容的品类、属性等，或是内容的ID列表
        date_range: 日期范围，如果为None，则默认为过去90天
        category_info: 内容分类信息
        limit: 限制返回的video_id数量，如果为-1，则不限制
    """
    # 生成SQL
    sql = generate_contents_query_sql(
        contents_query_conditions=contents_query_conditions,
        date_range=date_range,
        category_info=category_info,
        limit=limit,
    )
    logger.info(f"获取视频内容数据SQL: {sql}")

    # 获取内容数据
    chclient = ChClient()
    df = chclient.query_to_df(chclient.dw_db, sql)
    if df.empty:
        logger.warning(f"get_contents_data 查询数据为空，SQL: {sql}")
        return pd.DataFrame()

    # 处理维度数据
    df = process_dim_tuples(df)

    # 处理attributes字段
    df = process_attributes(df)

    return df


def process_dim_tuples(df: pd.DataFrame) -> pd.DataFrame:
    """处理维度数据字符串，展开为各个维度列"""

    def parse_dim_tuples(dim_tuples_str):
        if not dim_tuples_str or dim_tuples_str == "":
            return []
        # 解析字符串格式：'"dim_type1","dim_id1",0.5|"dim_type2","dim_id2",0.3'
        tuples = []
        for item in dim_tuples_str.split("|"):
            if not item:
                continue
            # 去掉引号，分割字符串
            parts = item.replace('"', "").split(",")
            if len(parts) == 3:
                dim_type, sub_dim_id, ratio = parts
                tuples.append((dim_type, sub_dim_id, float(ratio)))
        return tuples

    # 解析维度数据
    dim_tuples = df["dim_tuples"].apply(parse_dim_tuples)

    # 获取所有唯一的维度类型
    dim_types = set()
    for tuples in dim_tuples:
        dim_types.update(dim_type for dim_type, _, _ in tuples)

    # 为每个维度类型创建新列
    for dim_type in dim_types:
        df[f"{DIM_RATIO_PREFIX}{dim_type}"] = dim_tuples.apply(
            lambda tuples: [
                (sub_dim_id, ratio)
                for t_dim_type, sub_dim_id, ratio in tuples
                if t_dim_type == dim_type
            ]
        )

    # 删除原始的 dim_tuples 列
    df = df.drop("dim_tuples", axis=1)

    return df


def process_attributes(df: pd.DataFrame) -> pd.DataFrame:
    """处理attributes字段，展开为各个属性列"""
    import json

    def extract_mapped_values(attr_str):
        if not attr_str or attr_str == "":
            return {}

        try:
            attr_dict = json.loads(attr_str)
            result = {}

            for key, value_info in attr_dict.items():
                if isinstance(value_info, dict) and "mapped_value" in value_info:
                    # 使用mapped_value作为列值，如果为空则设为空列表
                    mapped_value = value_info["mapped_value"]

                    # 为非固定字段添加 attribute_ 前缀（固定字段：topic, hash_tags）
                    if key in ["topic", "hash_tags"]:
                        column_name = key  # 固定字段保持原名
                    else:
                        column_name = f"attribute_{value_info.get('name', key)}"  # 非固定字段添加前缀

                    if mapped_value is None or mapped_value == "" or mapped_value == []:
                        result[column_name] = []
                    elif isinstance(mapped_value, str):
                        # 如果是字符串，尝试按 "、" 或 "，" 分割成列表
                        if "、" in mapped_value:
                            result[column_name] = [
                                item.strip()
                                for item in mapped_value.split("、")
                                if item.strip()
                            ]
                        elif "，" in mapped_value:
                            result[column_name] = [
                                item.strip()
                                for item in mapped_value.split("，")
                                if item.strip()
                            ]
                        else:
                            # 如果没有分隔符，将整个字符串作为单元素列表
                            result[column_name] = (
                                [mapped_value.strip()] if mapped_value.strip() else []
                            )
                    else:
                        # 其他情况（已经是列表等），直接使用
                        result[column_name] = mapped_value

            return result
        except (json.JSONDecodeError, KeyError, TypeError):
            return {}

    # 处理attributes字段
    attributes_list = df["attributes"].apply(extract_mapped_values).tolist()
    attributes_df = pd.DataFrame(attributes_list)

    # 将所有NaN值替换为空列表
    for col in attributes_df.columns:
        mask = attributes_df[col].isna()
        attributes_df.loc[mask, col] = [[] for _ in range(mask.sum())]

    # 合并展开的属性到原始DataFrame
    df = pd.concat([df.drop("attributes", axis=1), attributes_df], axis=1)

    return df


def get_all_video_categories() -> List[CategoryInfo]:
    """
    获取所有视频品类
    """
    category_data_service = CategoryDataService.get_instance()

    # 使用集合来存储每个品类组合
    categories_set = set()

    for attr in category_data_service.video_attributes:
        # 使用元组作为键
        category = CategoryInfo(
            category1=attr.category1,
            category2=attr.category2,
            category3=attr.category3,
            category4=attr.category4,
        )

        # 如果这个品类还没有记录，初始化其属性字典
        if category not in categories_set:
            categories_set.add(category)

    return list(categories_set)


def get_all_filter_groups() -> List[FilterGroup]:
    categories = get_all_video_categories()
    filter_groups = []
    for category in categories:
        filter_groups.append(FilterGroup(category_info=category, attr=None))
    return filter_groups


if __name__ == "__main__":
    # aca = get_all_video_categories()
    df = get_contents_data(
        contents_query_conditions=[
            "hasAny(categories, ['时尚'])",
            "hasAny(sub_categories, ['穿搭'])",
        ],
        date_range=(date(2025, 6, 1), date(2025, 7, 31)),
        limit=10,
    )
    print(df)
