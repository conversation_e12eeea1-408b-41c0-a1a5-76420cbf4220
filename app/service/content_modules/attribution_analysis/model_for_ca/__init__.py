import time
import traceback
import numpy as np
from typing import List, Optional, Tuple, Dict, Any
from datetime import date

from app.logger import logger
from app.utils.dsl_info import DSLFieldInfo
from app.service.content_modules.attribution_analysis.model.utils import (
    get_model_name,
    get_category_and_attribute_from_filters,
    FilterGroup,
)
from app.service.content_modules.attribution_analysis.model.attribution_model import (
    ModelPredictionResult,
)
from app.service.content_modules.attribution_analysis.model_for_ca.utils import (
    get_all_filter_groups,
    get_contents_data,
)
from app.service.content_modules.attribution_analysis.model_for_ca.feature import (
    FeatureMatrixBuilder,
)
from app.service.content_modules.attribution_analysis.model_for_ca.attribution_model import (
    ContentAttributionModel,
)


class AttributionAnalysisModel:
    def __init__(self):
        self.feature_builder = FeatureMatrixBuilder()
        self.attribution_model = ContentAttributionModel()

    def train_all(self) -> Dict[str, Any]:
        filter_groups = get_all_filter_groups()
        all_models = {}

        for i, filter_group in enumerate(filter_groups):
            model_name = get_model_name(filter_group)
            log_prefix = f"[{i + 1:04d}/{len(filter_groups):04d}] 模型 {model_name}"

            try:
                logger.info(f"{log_prefix} 开始训练，filter_group={filter_group}")
                start_time = time.time()

                # 训练模型
                model_result = self.train(filter_group)

                # 将模型信息添加到字典中
                all_models[model_name] = model_result
                self.attribution_model.clear()
                logger.info(
                    f"{log_prefix} 训练成功，耗时 {time.time() - start_time:.2f} 秒, filter_group={filter_group}"
                )
            except Exception as e:
                logger.error(
                    f"{log_prefix} 训练失败: {e}\nfilter_group={filter_group}\n{traceback.format_exc()}"
                )
                continue

        return all_models

    def train(self, filter_group: FilterGroup) -> Any:
        try:
            start_time = time.time()
            query_conditions = self.get_query_conditions(filter_group)
            contents = get_contents_data(
                contents_query_conditions=query_conditions,
                category_info=filter_group.category_info,
            )
            if contents.empty:
                raise Exception("没有找到符合条件的内容数据")
            logger.info(
                f"get_contents_data 耗时: {time.time() - start_time:.2f} 秒，样本数: {len(contents)}"
            )

            # 构建特征矩阵
            start_time = time.time()
            features, feature_names, feature_groups = self.feature_builder.build(
                contents
            )
            logger.info(
                f"build_feature_matrix 耗时: {time.time() - start_time:.2f} 秒，特征数 {len(feature_names)}"
            )

            # 准备目标变量（按点赞数）
            target = contents["like_cnt"].astype(np.float32).to_numpy()

            # 执行模型训练
            start_time = time.time()
            model_result = self.attribution_model.train(
                X=features,
                y=target,
                feature_names=feature_names,
                feature_groups=feature_groups,
            )
            logger.info(
                f"train_attribution_model 耗时: {time.time() - start_time:.2f} 秒"
            )

            return model_result
        except Exception as e:
            logger.error(f"归因分析失败: {e}\n{traceback.format_exc()}")
            return {
                "error": f"归因分析失败: {e}",
            }

    @staticmethod
    def get_query_conditions(filter_group: FilterGroup) -> List[str]:
        """
        解析 filters，返回查询条件
        """
        where_condition = []

        category = filter_group.category_info
        # attr = filter_group.product_attr

        if category.category1:
            if isinstance(category.category1, list):
                category1_str = ",".join(f"'{c}'" for c in category.category1)
                where_condition.append(f"hasAny(categories, [{category1_str}])")
            elif isinstance(category.category1, str):
                where_condition.append(f"hasAny(categories, ['{category.category1}'])")
        if category.category2:
            if isinstance(category.category2, list):
                category2_str = ",".join(f"'{c}'" for c in category.category2)
                where_condition.append(f"hasAny(sub_categories, [{category2_str}])")
            elif isinstance(category.category2, str):
                where_condition.append(
                    f"hasAny(sub_categories, ['{category.category2}'])"
                )

        return where_condition

    def inference(
        self,
        filters: List[DSLFieldInfo],
        content_ids: List[str],
        date_range: Optional[Tuple[date, date]],
    ) -> ModelPredictionResult:
        try:
            if not content_ids or not filters:
                raise ValueError("content_ids 和 filters 不能为空")

            # 根据 filters 获取模型名称（哈希值）
            filter_group = get_category_and_attribute_from_filters(filters)

            # 模型名称
            model_name = get_model_name(filter_group)
            if not model_name:
                raise ValueError("无法获取模型名称")

            # 加载模型
            self.attribution_model.load_model(model_name)

            # 获取内容数据
            start_time = time.time()
            content_ids_str = "', '".join(content_ids)
            contents = get_contents_data(
                contents_query_conditions=[f"video_id IN ('{content_ids_str}')"],
                date_range=date_range,
                category_info=filter_group.category_info,
            )
            logger.info(
                f"get_contents_data 耗时: {time.time() - start_time:.2f} 秒，样本数 {len(contents)}"
            )

            # 构建特征矩阵
            start_time = time.time()
            features, feature_names, feature_groups = self.feature_builder.build(
                contents
            )
            logger.info(
                f"build_feature_matrix 耗时: {time.time() - start_time:.2f} 秒，特征数 {len(feature_names)}"
            )

            # 获取目标变量
            target = contents["like_cnt"].astype(float).to_numpy()

            # 执行模型推理
            start_time = time.time()
            attribution_result = self.attribution_model.predict(
                X=features,
                y=target,
                feature_names=feature_names,
                feature_groups=feature_groups,
            )
            logger.info(f"predict 耗时: {time.time() - start_time:.2f} 秒")

            return attribution_result
        except Exception as e:
            logger.error(f"归因分析失败: {e}\n{traceback.format_exc()}")
            raise e
