"""
视频内容归因分析模型

基于README.md中定义的5个特征维度，专门用于分析视频内容的归因效果：
1. 观众画像维度 (Audience Profile)
2. 内容基础维度 (Content Basics)
3. 商品与内容属性维度 (Product & Content Attributes)
4. 商业推广维度 (Business Promotion)
5. 用户反馈维度 (User Engagement)

继承原有AttributionModel的核心功能，但针对视频内容数据进行了特征维度适配。
"""

import numpy as np
from typing import Dict, Any, List, Optional

from app.logger import logger  # noqa: E402
from app.service.content_modules.attribution_analysis.model.utils import (  # noqa: E402
    DIM_RATIO_PREFIX,
)
from app.service.content_modules.attribution_analysis.model.model_loader import (  # noqa: E402
    save_models,
)
from app.service.content_modules.attribution_analysis.model.attribution_model import (  # noqa: E402
    ModelFeatureDimensions,
    ModelTrainingResult,
    ModelPredictionResult,
    AttributionModel as BaseAttributionModel,
)

# 内容归因分析模型的保存路径
CONTENT_ATTRIBUTION_MODEL_SAVE_PATH = "attribution_model/content_assistant_models.pkl"

# 行业领先者所占百分比（默认为前5%）
LEADERS_PERCENTAGE = 0.05

# 内容助手特征维度定义
# 基于README.md中定义的5个维度进行特征分组
CONTENT_MAIN_DIMS = {
    "观众画像维度": lambda feature_groups: [
        g for g in feature_groups if g.startswith(DIM_RATIO_PREFIX)
    ],
    "内容基础维度": lambda feature_groups: [
        g
        for g in feature_groups
        if g
        in [
            "desc",
            "duration",
            "topic",
            "hash_tags",
            "rank_type",
            "create_date",
            "time_slot",  # 包含时间段特征
            "author_fans_range",  # 包含作者粉丝等级特征
        ]
    ],
    "商品与内容属性维度": lambda feature_groups: [
        g for g in feature_groups if g.startswith("attribute_")
    ],
    "商业推广维度": lambda feature_groups: [
        g
        for g in feature_groups
        if g
        in [
            "bring_goods",
            "is_bring_goods",
            "promotion_type",
            "sales_amount",
            "sales_volume",
        ]
    ],
    "用户反馈维度": lambda feature_groups: [
        g
        for g in feature_groups
        if g
        in [
            "view_cnt",
            "like_cnt",
            "favourite_cnt",
            "share_cnt",
            "comment_cnt",
            "hot_comment_words",
        ]
    ],
}


class ContentAttributionModel(BaseAttributionModel):
    """内容助手归因分析模型

    继承自原有的AttributionModel，主要变化：
    1. 修改特征维度划分，适配视频内容数据结构
    2. 使用专用的模型保存路径
    3. 针对内容数据的特征维度优化分析逻辑
    """

    def __init__(self):
        """初始化内容归因分析模型"""
        super().__init__()
        # 使用内容助手专用的模型保存路径
        self.model_save_path = CONTENT_ATTRIBUTION_MODEL_SAVE_PATH

    @staticmethod
    def save_all_models(
        models_dict: Dict[str, Dict[str, Any]],
        save_path: str = CONTENT_ATTRIBUTION_MODEL_SAVE_PATH,
    ) -> str:
        """保存所有内容助手模型到 S3

        Args:
            models_dict: 模型字典
            save_path: S3 中的保存路径

        Returns:
            str: 保存路径

        Raises:
            ValueError: 当 models_dict 为空时
            Exception: 保存失败时
        """
        return save_models(models_dict, save_path)

    def _analyze_feature_dimensions(
        self,
        group_importance: Dict[str, float],
        feature_groups: List[str],
    ) -> ModelFeatureDimensions:
        """分析特征维度（重写父类方法以使用内容助手的维度定义）

        Args:
            group_importance: 特征组重要性字典
            feature_groups: 特征组列表

        Returns:
            ModelFeatureDimensions: 特征维度分析结果
        """
        dimensions = {}
        dimension_importance = {}
        relative_group_importance_within_dimension = {}

        for dim_name, dim_groups_func in CONTENT_MAIN_DIMS.items():
            # 使用lambda函数动态生成特征组
            groups = dim_groups_func(feature_groups)

            # 过滤出实际存在于group_importance中的特征组
            groups = [g for g in groups if g in group_importance]

            if not groups:
                continue

            # 维度划分
            dimensions[dim_name] = groups

            # 维度重要性
            dimension_importance[dim_name] = sum(
                group_importance[g] if g in group_importance else 0 for g in groups
            )

            # 维度内特征组的相对重要性
            group_importance_dict = {
                g: group_importance[g] if g in group_importance else 0 for g in groups
            }
            relative_group_importance = self._calculate_relative_importance(
                group_importance_dict
            )
            if relative_group_importance:
                relative_group_importance_within_dimension[dim_name] = (
                    relative_group_importance
                )

        # 整理结果
        cache_feature_dimensions = ModelFeatureDimensions(
            dimensions=dimensions,
            importance=dimension_importance,
            relative_group_importance_within_dimension=relative_group_importance_within_dimension,
        )

        return cache_feature_dimensions

    def train(
        self,
        X: np.ndarray,
        y: np.ndarray,
        feature_names: List[str],
        feature_groups: List[str],
    ) -> ModelTrainingResult:
        """训练内容助手归因分析模型并计算特征重要性

        Args:
            X: 特征矩阵，shape=(n_samples, n_features)
            y: 目标变量，shape=(n_samples,)
            feature_names: 特征名列表
            feature_groups: 特征组列表，每个特征名以对应的特征组为前缀

        Returns:
            ModelTrainingResult: 训练结果
        """
        try:
            logger.info("开始训练内容助手归因分析模型...")

            # 调用父类的训练方法
            result = super().train(X, y, feature_names, feature_groups)

            logger.info("内容助手归因分析模型训练完成")
            return result

        except Exception as e:
            logger.error(f"内容助手归因分析模型训练失败: {str(e)}")
            raise Exception(f"内容助手归因分析模型训练失败: {str(e)}")

    def predict(
        self,
        X: np.ndarray,
        y: np.ndarray,
        feature_names: List[str],
        feature_groups: List[str],
    ) -> ModelPredictionResult:
        """使用已训练的内容助手模型进行预测和特征重要性分析

        Args:
            X: 特征矩阵，shape=(n_samples, n_features)
            y: 目标变量，shape=(n_samples,)
            feature_names: 特征名列表
            feature_groups: 特征组列表，每个特征名以对应的特征组为前缀

        Returns:
            ModelPredictionResult: 预测结果
        """
        try:
            logger.info("开始使用内容助手归因分析模型进行预测...")

            # 调用父类的预测方法
            result = super().predict(X, y, feature_names, feature_groups)

            logger.info("内容助手归因分析模型预测完成")
            return result

        except Exception as e:
            logger.error(f"内容助手归因分析模型预测失败: {str(e)}")
            raise Exception(f"内容助手归因分析模型预测失败: {str(e)}")

    def _calculate_weighted_average_with_comment_filter(
        self,
        X: np.ndarray,
        weights: np.ndarray,
        feature_names: List[str],
        sample_indices: Optional[np.ndarray] = None,
    ) -> Dict[str, float]:
        """
        计算加权平均值，针对视频内容数据的特殊处理

        对于视频内容数据，我们需要特别处理评论相关特征：
        - 对comment相关特征，忽略comment_cnt为0的样本
        - 其他特征正常计算加权平均

        Args:
            X: 特征矩阵，shape=(n_samples, n_features)
            weights: 权重数组，shape=(n_samples,)
            feature_names: 特征名列表
            sample_indices: 可选的样本索引，如果提供则只计算这些样本的平均值

        Returns:
            Dict[str, float]: 特征名到平均值的映射
        """
        if sample_indices is not None:
            X_subset = X[sample_indices]
            weights_subset = weights[sample_indices]
        else:
            X_subset = X
            weights_subset = weights

        feature_means = {}

        # 找出comment_cnt特征的索引
        comment_cnt_idx = None
        comment_features = set()  # 所有需要过滤的comment相关特征

        for i, feature_name in enumerate(feature_names):
            if feature_name.startswith("comment") or "comment" in feature_name:
                comment_features.add(feature_name)
                if feature_name == "comment_cnt":
                    comment_cnt_idx = i

        # 如果存在comment_cnt特征，计算有效样本的mask
        valid_comment_mask = None
        if comment_cnt_idx is not None:
            valid_comment_mask = X_subset[:, comment_cnt_idx] > 0

        # 计算每个特征的平均值
        for i, feature_name in enumerate(feature_names):
            if feature_name in comment_features and valid_comment_mask is not None:
                # 对于comment相关特征，只考虑comment_cnt > 0的样本
                if np.any(valid_comment_mask):
                    # 只对comment_cnt > 0的样本计算加权平均
                    valid_values = X_subset[valid_comment_mask, i]
                    valid_weights = weights_subset[valid_comment_mask]
                    feature_means[feature_name] = float(
                        np.average(valid_values, weights=valid_weights)
                    )
                else:
                    # 如果没有有效样本，设为0
                    feature_means[feature_name] = 0.0
            else:
                # 对于其他特征，正常计算加权平均
                feature_means[feature_name] = float(
                    np.average(X_subset[:, i], weights=weights_subset)
                )

        return feature_means
