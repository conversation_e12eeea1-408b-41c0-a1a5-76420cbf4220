import numpy as np
import pandas as pd
from typing import Tuple, List

from app.logger import logger
from app.service.content_modules.attribution_analysis.model.feature import (
    FeatureMatrixBuilder as BaseFeatureMatrixBuilder,
)
from app.service.content_modules.attribution_analysis.model.utils import (
    DIM_RATIO_PREFIX,
)


class FeatureMatrixBuilder(BaseFeatureMatrixBuilder):
    """
    视频内容归因分析特征矩阵构建器
    """

    def get_excluded_columns(self) -> set:
        """获取需要排除的列集合，适配视频内容数据结构"""
        return {
            "video_id",  # 视频ID，主键
            "desc",  # 视频描述，文本过长不适合直接作为特征
            "author_account_id",  # 作者账号ID，标识符
            "author_name",  # 作者名称，标识符
            "daren_id",  # 达人ID，标识符
            "create_time",  # 创建时间，将被处理为时间段特征
            "author_fans_cnt",  # 作者粉丝数，将被处理为粉丝等级特征
            "like_cnt",  # 点赞数，目标变量
            "audience_profile",  # 受众画像，暂时为空不做分析（且有dim特征做补充了）
            "data_source",  # 数据来源
            "data_platform",  # 数据平台
            "categories",  # 视频分类
            "sub_categories",  # 视频子分类
            f"{DIM_RATIO_PREFIX}city",  # 不再使用city作为特征
        }

    def process_special_features(
        self, data: pd.DataFrame, excluded_columns: set
    ) -> Tuple[np.ndarray, List[str], List[str]]:
        """处理特殊特征（视频时间段特征和作者粉丝等级特征）

        当前版本处理视频发布时间段特征和作者粉丝等级特征

        Args:
            data: 输入数据
            excluded_columns: 已排除的列集合，此方法处理的列应加入此集合

        Returns:
            Tuple[np.ndarray, List[str], List[str]]: 特征矩阵、特征名列表和组名列表
        """
        all_encoded = []
        all_column_names = []
        all_group_names = []

        # 处理视频发布时间段特征
        if "create_time" in data.columns:
            excluded_columns.add("create_time")
            time_encoded, time_column_names, time_group_names = (
                self._process_time_slot_features(data)
            )
            all_encoded.append(time_encoded)
            all_column_names.extend(time_column_names)
            all_group_names.extend(time_group_names)

        # 处理作者粉丝等级特征
        if "author_fans_cnt" in data.columns:
            excluded_columns.add("author_fans_cnt")
            fans_encoded, fans_column_names, fans_group_names = (
                self._process_author_fans_range_features(data)
            )
            all_encoded.append(fans_encoded)
            all_column_names.extend(fans_column_names)
            all_group_names.extend(fans_group_names)

        # 合并所有特征
        if all_encoded:
            combined_encoded = np.concatenate(all_encoded, axis=1)
            return combined_encoded, all_column_names, all_group_names
        else:
            # 如果没有任何特殊特征，返回空特征
            return np.zeros((len(data), 0)), [], []

    def _process_time_slot_features(
        self, data: pd.DataFrame
    ) -> Tuple[np.ndarray, List[str], List[str]]:
        """处理视频发布时间段特征

        将create_time转换为时间段分类特征，按照用户提供的分类标准
        """

        def get_time_slot(create_time):
            """根据创建时间获取时间段"""
            if pd.isna(create_time):
                return "unknown"

            try:
                # 如果是字符串，尝试解析为datetime
                if isinstance(create_time, str):
                    dt = pd.to_datetime(create_time)
                else:
                    dt = create_time

                # 提取小时和分钟
                hour = dt.hour
                minute = dt.minute
                time_str = f"{hour:02d}:{minute:02d}"

                # 按照用户提供的分类标准
                if "00:00" <= time_str < "06:00":
                    return "00:00-06:00"
                elif "06:00" <= time_str < "09:00":
                    return "06:00-09:00"
                elif "09:00" <= time_str < "12:00":
                    return "09:00-12:00"
                elif "12:00" <= time_str < "14:00":
                    return "12:00-14:00"
                elif "14:00" <= time_str < "17:00":
                    return "14:00-17:00"
                elif "17:00" <= time_str < "19:00":
                    return "17:00-19:00"
                elif "19:00" <= time_str < "22:00":
                    return "19:00-22:00"
                elif "22:00" <= time_str < "24:00":
                    return "22:00-24:00"
                else:
                    return "unknown"
            except Exception as e:
                logger.warning(f"解析create_time时出错: {create_time}, error: {str(e)}")
                return "unknown"

        # 应用时间段分类
        time_slots = data["create_time"].apply(get_time_slot)

        # 获取所有可能的时间段
        all_time_slots = [
            "00:00-06:00",
            "06:00-09:00",
            "09:00-12:00",
            "12:00-14:00",
            "14:00-17:00",
            "17:00-19:00",
            "19:00-22:00",
            "22:00-24:00",
            "unknown",
        ]

        # 创建独热编码
        encoded = np.zeros((len(data), len(all_time_slots)))
        for i, slot in enumerate(time_slots):
            if slot in all_time_slots:
                j = all_time_slots.index(slot)
                encoded[i, j] = 1

        column_names = [f"time_slot_{slot}" for slot in all_time_slots]
        group_names = ["time_slot"] * len(all_time_slots)

        return encoded, column_names, group_names

    def _process_author_fans_range_features(
        self, data: pd.DataFrame
    ) -> Tuple[np.ndarray, List[str], List[str]]:
        """处理作者粉丝等级特征

        将author_fans_cnt转换为粉丝等级分类特征，按照用户提供的分类标准
        """

        def get_fans_range(fans_cnt):
            """根据粉丝数获取粉丝等级"""
            if pd.isna(fans_cnt):
                return "unknown"

            try:
                # 转换为数值类型
                if isinstance(fans_cnt, str):
                    fans_cnt = float(fans_cnt)

                fans_cnt = int(fans_cnt)

                # 按照用户提供的分类标准
                if fans_cnt < 10000:
                    return "<1w"
                elif 10000 <= fans_cnt < 100000:
                    return "1w~10w"
                elif 100000 <= fans_cnt < 1000000:
                    return "10w~100w"
                elif 1000000 <= fans_cnt < 5000000:
                    return "100w~500w"
                else:
                    return ">500w"
            except Exception as e:
                logger.warning(
                    f"解析author_fans_cnt时出错: {fans_cnt}, error: {str(e)}"
                )
                return "unknown"

        # 应用粉丝等级分类
        fans_ranges = data["author_fans_cnt"].apply(get_fans_range)

        # 获取所有可能的粉丝等级
        all_fans_ranges = [
            "<1w",
            "1w~10w",
            "10w~100w",
            "100w~500w",
            ">500w",
            "unknown",
        ]

        # 创建独热编码
        encoded = np.zeros((len(data), len(all_fans_ranges)))
        for i, fans_range in enumerate(fans_ranges):
            if fans_range in all_fans_ranges:
                j = all_fans_ranges.index(fans_range)
                encoded[i, j] = 1

        column_names = [
            f"author_fans_range_{fans_range}" for fans_range in all_fans_ranges
        ]
        group_names = ["author_fans_range"] * len(all_fans_ranges)

        return encoded, column_names, group_names
