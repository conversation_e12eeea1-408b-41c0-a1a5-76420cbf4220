import os
import joblib
import tempfile
import argparse
import traceback
from typing import Optional

from app.logger import logger
from app.service.content_modules.attribution_analysis.model_for_ca import (
    AttributionAnalysisModel,
)
from app.service.content_modules.attribution_analysis.model.upload_model import (
    upload_model,
)


def train_all_models(
    local_path: Optional[str] = None,
    upload_to_staging: bool = True,
    upload_to_prod: bool = False,
) -> bool:
    """训练并保存所有模型

    Args:
        local_path: 本地保存路径，如果为 None 则生成临时路径
        upload_to_staging: 是否上传到 STAGING 环境
        upload_to_prod: 是否上传到 PROD 环境

    Returns:
        bool: 训练和上传是否成功
    """
    temp_path = None
    try:
        attribution_analysis = AttributionAnalysisModel()
        # 训练所有模型
        all_models = attribution_analysis.train_all()

        # 确保有本地保存路径
        if not local_path:
            temp_file = tempfile.NamedTemporaryFile(
                prefix="attribution_model_",
                suffix=".joblib",
                delete=False,
            )
            temp_path = temp_file.name
            temp_file.close()
            local_path = temp_path
        local_path = os.path.abspath(local_path)

        # 保存模型到本地
        try:
            joblib.dump(all_models, local_path)
            logger.info(f"模型已保存到本地: {local_path}")
        except Exception as e:
            logger.error(f"保存模型到本地失败: {e}\n{traceback.format_exc()}")
            if temp_path:
                os.unlink(temp_path)
            return False

        # 确定要上传的环境
        environments = []
        if upload_to_staging:
            environments.append("STAGING")
        if upload_to_prod:
            environments.append("PROD")

        # 上传模型到指定环境
        if environments:
            success = upload_model(
                model_path=local_path,
                environments=environments,
            )
        else:
            logger.info("当前训练流程无需上传模型")
            success = True

        # 如果使用了临时文件，清理它
        if temp_path:
            os.unlink(temp_path)

        return success

    except Exception as e:
        logger.error(f"训练模型过程中发生错误: {e}\n{traceback.format_exc()}")
        if temp_path:
            os.unlink(temp_path)
        return False


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="训练归因分析模型")
    parser.add_argument(
        "--local-path",
        type=str,
        default="./ca_attribution_model.joblib",
        help="本地保存路径，如果指定则将模型保存到该路径",
    )
    parser.add_argument(
        "--upload-to-staging",
        action="store_true",
        default=True,
        help="是否上传到 STAGING 环境（默认：是）",
    )
    parser.add_argument(
        "--upload-to-prod",
        action="store_true",
        default=True,
        help="是否上传到 PROD 环境（默认：否）",
    )

    args = parser.parse_args()

    success = train_all_models(
        local_path=args.local_path,
        upload_to_staging=args.upload_to_staging,
        upload_to_prod=args.upload_to_prod,
    )

    # 设置退出码
    exit(0 if success else 1)
