from __future__ import annotations  # to support recursive type

import json
import re
import html
import traceback
from typing import Literal, ClassVar, Union, Any
from collections import defaultdict

import yaml
from langchain_core.messages import (
    SystemMessage,
    AIMessage,
    HumanMessage,
    BaseMessage,
)
from langfuse.decorators import langfuse_context, observe
from langgraph.graph import StateGraph
from langchain_openai import ChatOpenAI
from langgraph.types import Command, interrupt
from pydantic import BaseModel, Field
from retry import retry
from typing_extensions import List, Optional, Dict

from app.category_config import CategoryInfo, CategoryWhitelistConfig
from app.ch.daren_client import DarenClient
from app.ch.model import DimShop, ModelAggDarenBasicInfo
from app.ch.orm import ChClient
from app.ch.shop_client import ShopClient
from app.conversation_processor.tool_functions import ChoiceQuestion
from app.conversation_type import SnsPlatform, sns_platform_to_data_platform_map
from app.db.database import langgraph_checkpointer
from app.db.repository.user_repository import get_user
from app.db.vo import UserVo
from app.env import SIHEGPT_STAGE
from app.logger import logger
from app.middleware.middleware import get_whitelist_checker
from app.service.content_modules import filter_to_dsl, ALL_PRODUCT_MODULES
from app.service.content_modules.content_module_prompts import sns_platform_prompt
from app.service.content_modules.filter_to_dsl import filter_name_to_field_map
from app.service.ner import ner_service
from app.service.ner.shop_select_branch import ShopMatchResponse
from app.utils.category_data import CategoryDataService
from app.service.common_service import AccumulateRateLimiter
from app.service.content_modules import generate_chapter_content, ContentModuleParams
from app.service.content_modules.filter import Filter, FilterOperator, Matcher
from app.service.activity.collector import collect_event
from app.service.content_modules.module_desc import get_module_desc_manager

from app.service.search_agent.history import (
    HousekeeperHistory,
    structure_history,
    simplify_history,
    restore_selected_sections,
    SectionSelection,
)
from app.service.search_agent.data_def import (
    HousekeeperState,
    PlanWithResults,
    ChapterTask,
    ChapterBlock,
    ChapterResult,
    HousekeeperProgressData,
    MessageAnalysisResponse,
    VideoSearchParams,
    TaskPlanningResponse,
    ModuleCallParams,
    ModuleCallResult,
    DarenMatcher,
    VideoMatcher,
    LLMGenFilter as LLMGenFilter,
    ContentModuleLlmParams,
    AssistantName,
    SubmoduleDecision,
)
from app.service.search_agent.llm_def import (
    llm_plan_ecommerce_a,
    llm_plan_ecommerce_b,
    llm_generate_module_call_params,
    llm_housekeeper,
)
from app.service.ner.ner_result_data import NerResult, NamedEntityType, NerResultItem
from app.service.search_agent.search_agent import retrieve, search_video
from app.service.search_agent.utils import (
    get_today_string,
    get_today_minus_n_string,
    Question,
    DOC_REF_ID,
    count_chinese_characters,
    remove_video_ref,
    remove_any_html_tag,
    substitute_table,
    shorten_markdown_tables,
    remove_chart_blocks,
)
from app.service.content_assistant.data_def import (
    ContentAssistantState,
    ArticleInfo,
    VideoInfo,
)
from app.utils.daren_utils import get_daren_markdown_link_text
from app.utils.dsl_info import DimensionInfo
from app.utils.langfuse_utils import (
    get_langgraph_prod_prompt,
)
import app.service.document_service as document_service
from app.service.ner.ner_service import ner_result_add_entity
from app.service.ner.entity_source import NamedEntity

# 生成文章从二级标题开始。原因是 H1 标题会作为文档标题，import_markdown_doc 会删除其他的 H1
CHAPTER_START_LEVEL = 2
# 从视频搜索结果中，选择前 N 条发送给大模型
N_VIDEO_SEARCH_RESULT = 10
# 限制喂给LLM的markdown内容中表格长度
LLM_LIMIT = 20


def collect_content_module_params_event(
    state,
    chapter_task: ChapterTask,
    module_call_params,
    filters: List[Filter],
    dimensions: List,
) -> None:
    """
    收集内容模块参数事件

    Args:
        state: 助手状态对象
        chapter_task: 章节任务对象
        module_call_params: 模块调用参数
        filters: 过滤器列表
        dimensions: 维度列表
    """
    try:
        # 获取助手类型
        assistant_name = getattr(state, "assistant_name", "unknown")
        if hasattr(assistant_name, "value"):
            assistant_name = assistant_name.value

        # 提取过滤器信息
        filters_info = {}
        for filter_item in filters:
            filters_info[filter_item.name] = filter_item.value

        # 提取维度信息
        dimensions_info = [dim.field_name for dim in dimensions]

        collect_event(
            event_name="housekeeper:content_module_params",
            user_id=str(state.user_id),
            source="backend",
            event_args={
                "assistant_type": assistant_name,
                "module_name": chapter_task.module_call.模块名称 or "",
                "module_params": {
                    "filters": filters_info,
                    "dimensions": dimensions_info,
                    "limit": module_call_params.limit,
                    "sns_platform": (
                        state.sns_platform.value
                        if hasattr(state.sns_platform, "value")
                        else str(state.sns_platform)
                    ),
                },
            },
        )
    except Exception as e:
        # 事件收集失败不应该影响主流程
        logger.warning(
            f"Failed to collect housekeeper:content_module_params event: {e}"
        )


class LongContentResponse(BaseModel):
    """将长文本回复拆成若干次回复"""

    content: str = Field(description="每次回复在2000个字以内")
    has_more: bool = Field(description="是否还有内容需要继续输出")


def query_product_category(product_category: CategoryInfo) -> CategoryInfo:
    if product_category.category3:
        return product_category
    # 目前要求用户选 三级分类
    category_options = [
        CategoryInfo(category1="食品饮料", category2="酒类", category3="白酒"),
        CategoryInfo(
            category1="运动户外",
            category2="户外/登山/野营/旅行用品",
            category3="垂钓装备",
        ),
    ]
    result: str = interrupt(
        ChoiceQuestion(
            question="请选择产品分类",
            options=[o.category3 for o in category_options],
            allow_other=False,
        )
    )[0]

    for o in category_options:
        if o.category3 == result:
            return o
    raise ValueError(f"未知的用户选择分类: {result}")


def select_shop(llm_shop_match: ShopMatchResponse) -> List[NerResultItem]:
    final_shop_list = []
    option_name_list = []
    for match in llm_shop_match.match_list:
        if len(match.matches) <= 1:
            final_shop_list.extend(match.matches)
        else:
            option_name_list.extend(match.matches)

    if len(option_name_list) == 0:
        return [NerResultItem(name=o) for o in final_shop_list]
    shop_name_list = option_name_list
    shop_client = ShopClient()
    daren_client = DarenClient()
    shop_list: List[Optional[DimShop]] = []
    for name in shop_name_list:
        shop = shop_client.get_shop_by_name(name)
        shop_list.append(shop)

    shop_daren_list: List[Optional[ModelAggDarenBasicInfo]] = []
    for shop in shop_list:
        if shop is None:
            shop_daren_list.append(None)
            continue
        daren_id = shop.shop_douyin_id
        if not daren_id:
            shop_daren_list.append(None)
            continue
        daren = daren_client.get_daren_by_id(str(daren_id))
        shop_daren_list.append(daren)

    shop_linked_option = []
    for shop_name, daren in zip(shop_name_list, shop_daren_list):
        if daren is None:
            shop_linked_option.append(shop_name)
            continue
        sns_platform = sns_platform_to_data_platform_map.inv[str(daren.data_platform)]
        shop_linked_option.append(
            get_daren_markdown_link_text(
                shop_name,
                str(daren.daren_id),
                str(daren.daren_account_id),
                sns_platform,
            )
            + f"（{daren.fans_count_total} 粉丝）"
        )

    result_list: List[str] = interrupt(
        ChoiceQuestion(
            question=f"有多个相关店铺，请选择您关心的店铺",
            options=shop_linked_option,
            is_single_choice=False,
            allow_other=False,
        )
    )
    selected_name_list = []
    for result in result_list:
        for option, name in zip(shop_linked_option, shop_name_list):
            if result[:20] == option[:20]:  # hack: 避免粉丝数变化，导致匹配失败
                selected_name_list.append(name)
                break
    final_shop_list.extend(selected_name_list)
    return [NerResultItem(name=name) for name in final_shop_list]


class HousekeeperAgentBase:
    """管家Agent基类，集合所有相关函数"""

    HOUSE_KEEPER_SCORE_KEY: ClassVar[str] = "housekeeper_score"

    def __init__(self) -> None:
        # 创建状态图
        self.graph_builder = StateGraph(HousekeeperState)
        self.setup_graph()
        self.llm_assistant = llm_housekeeper
        self.prompt_names = {
            "select_history_sections": "housekeeper_select_useful_history_sections",
            "refactor_user_question": "housekeeper_refactor_user_question",
            "analyze_previous_conversation": "housekeeper_analyze_previous_conversation",
            "task_plan_a": "ecommerce_assistant_task_plan_a",
            "task_plan_b": "ecommerce_assistant_task_plan_b",
            "task_plan_title_and_think": "housekeeper_task_plan_title_and_think",
        }
        self.MessageAnalysisResponse = MessageAnalysisResponse
        self.llm_plan_a = llm_plan_ecommerce_a
        self.llm_plan_b = llm_plan_ecommerce_b

    def setup_graph(self) -> None:
        """设置状态图的节点和边。你可以运行本文件的 main 函数，查看状态图的结构"""
        self.graph_builder.add_node("history_prune_node", self.history_prune_node)
        self.graph_builder.add_node(
            "user_question_refactor_node", self.user_question_refactor_node
        )
        self.graph_builder.add_node(
            "conversation_analysis_node", self.conversation_analysis_node
        )
        self.graph_builder.add_node(
            "search_video_and_reply_node", self.search_video_and_reply_node
        )
        self.graph_builder.add_node(
            "entity_recognition_node", self.entity_recognition_node
        )
        self.graph_builder.add_node("process_category_node", self.process_category_node)
        self.graph_builder.add_node("plan_node", self.plan_node)
        self.graph_builder.add_node("do_plan_steps", self.do_plan_steps)
        self.graph_builder.add_node("summarize_answer", self.summarize_answer)
        self.graph_builder.add_node("simple_answer_node", self.simple_answer_node)

        # 设置入口点
        self.graph_builder.set_entry_point("history_prune_node")

        # 添加边
        self.graph_builder.add_edge("entity_recognition_node", "process_category_node")
        self.graph_builder.add_edge("do_plan_steps", "summarize_answer")
        self.graph_builder.add_edge("summarize_answer", "__end__")

        # 编译图
        self.housekeeper_graph = self.graph_builder.compile(
            checkpointer=langgraph_checkpointer,
        )
        self.housekeeper_graph.name = "housekeeper_graph"

    @staticmethod
    def create_housekeeper_input(
        user_input,
        user_id: int,
        conversation_id: str,
        thread_id: str,
        sns_platform: SnsPlatform,
        assistant_name: AssistantName,
        content_json: dict | None = None,
    ) -> HousekeeperState:
        if not isinstance(user_input, list):
            user_input = [user_input]
        # 将用户输入作为 refactored_question 的缺省值，之后会有节点更新
        refactored_question = user_input[-1]
        if isinstance(refactored_question, dict):
            refactored_question = refactored_question["content"]
        elif isinstance(refactored_question, BaseMessage):
            refactored_question = refactored_question.content
        refactored_question = str(refactored_question)
        return HousekeeperState(
            sns_platform=sns_platform,
            user_id=user_id,
            conversation_id=conversation_id,
            messages=user_input,
            thread_id=thread_id,
            entity_result=NerResult(),
            docs=[],
            videos=[],
            chat_history_without_question=user_input[:-1],
            refactored_question=refactored_question,
            searched_docs=[],
            searched_videos=[],
            plan=PlanWithResults(),
            plan_step_index=0,
            db_query_result_list=[],
            housekeeper_step_index=0,
            content_json=content_json,
            assistant_name=assistant_name,
        )

    @staticmethod
    def get_housekeeper_prompt() -> str:
        system_prompt = get_langgraph_prod_prompt(
            "house_keeper_prompt",
            # label='latest', # for debug
            today=get_today_string(),
        )
        return system_prompt

    @staticmethod
    def format_history_messages(
        messages: list[BaseMessage], max_messages: int | None = None
    ) -> list[dict]:
        """
        将历史消息格式化为字典列表
        """
        if max_messages is not None:
            messages = messages[-max_messages:]
        return [
            {"role": message.type, "content": message.content} for message in messages
        ]

    @observe
    def entity_recognition(self, refactored_question: str, at_list: list) -> NerResult:
        # 获取实体识别结果并处理成一个字典
        ner_result: NerResult = ner_service.ner(refactored_question)
        # 如果没有成功识别出Category则让LLM再做一次识别
        if not ner_result.product_category.category1:
            category_whitelist = CategoryWhitelistConfig().product_category_list
            max_retries = 2
            for attempt in range(max_retries):
                try:
                    prompt = get_langgraph_prod_prompt(
                        "housekeeper_user_question_category_recognition",
                        user_question=refactored_question,
                        category_list=category_whitelist,
                    )
                    category_result: (
                        CategoryInfo
                    ) = self.llm_assistant.with_structured_output(CategoryInfo).invoke(
                        prompt, name="user_question_category_recognition"
                    )  # type: ignore
                    assert CategoryWhitelistConfig().is_category_in_whitelist(
                        category_result
                    )
                    assert category_result.category1 != ""
                    ner_result.product_category = category_result
                    break
                except Exception as e:
                    logger.error(
                        f"尝试将用户的问题用LLM归类第 {attempt + 1}/{max_retries} 次失败: {e}"
                    )
                    logger.error(traceback.format_exc())
                    if attempt < max_retries - 1:
                        continue
                    else:
                        logger.error(
                            "尝试将用户的问题用LLM归类都失败了，该问题将没有类别"
                        )

        # 合并@实体和NER结果
        if at_list:
            at_entities_by_type = defaultdict(list)
            for at_entity in at_list:
                try:
                    entity_type = NamedEntityType(at_entity["type"])
                    at_entities_by_type[entity_type].append(
                        NamedEntity(
                            name=at_entity["name"],
                            entity_type=entity_type,
                            aliases=[],
                        )
                    )
                except ValueError:
                    logger.warning(f"未知的实体类型: {at_entity['type']}")

            for entity_type, entities in at_entities_by_type.items():
                # 清空该类型原有的NER结果
                if entity_type in ner_result.entity_dict_v2:
                    ner_result.entity_dict_v2[entity_type].clear()

                # 添加@的实体
                for entity in entities:
                    ner_result_add_entity(ner_result, entity)

        return ner_result

    @observe
    def history_prune_node(
        self, state: HousekeeperState
    ) -> Command[Literal["do_plan_steps"]]:
        """
        Housekeeper的入口节点
        历史对话修剪节点，选择有用的历史对话章节，并且将用户的问题结合历史记录重构
        original_history: 包含所有历史对话的 HousekeeperHistory 对象
        simplified_history: 简化后的历史对话，包含所有章节，较长的章节只包含头尾
        selected_history: 选择的有用历史章节，包含所有章节名，只有有用的章节包含内容
        """
        logger.info("------- history_prune_node -------")
        user_original_question = state.messages[-1].content
        formatted_history_messages = HousekeeperAgentBase.format_history_messages(
            state.messages, 10
        )
        original_history: HousekeeperHistory = structure_history(
            formatted_history_messages
        )
        simplified_history: HousekeeperHistory = simplify_history(original_history)
        selected_history = simplified_history  # 若选取失败，则selected_history直接使用simplified_history
        num_max_retries = 2
        for attempt in range(num_max_retries):
            try:
                select_prompt = get_langgraph_prod_prompt(
                    self.prompt_names["select_history_sections"],
                    simplified_history=simplified_history,
                    user_question=user_original_question,
                )
                selected_sections: SectionSelection = (  # type: ignore
                    self.llm_assistant.with_structured_output(SectionSelection).invoke(
                        select_prompt, name="select_useful_history_sections"
                    )
                )
                selected_history: HousekeeperHistory = restore_selected_sections(
                    selected_sections, original_history, simplified_history
                )
                break
            except Exception as e:
                logger.error(
                    f"尝试选择有用的历史章节 {attempt + 1}/{num_max_retries} 失败: {e}"
                )
                logger.error(traceback.format_exc())
        else:
            logger.error("尝试选择有用的历史章节失败，直接使用simplified_history")
        update_dict = {
            "refactored_question": user_original_question,
            "plan_step_index": 0,
            "selected_history": selected_history,
        }
        return Command(
            goto="user_question_refactor_node",
            update=update_dict,
        )

    @observe
    def user_question_refactor_node(
        self, state: HousekeeperState
    ) -> Command[Literal["conversation_analysis_node"]]:
        """
        该节点会在历史对话修剪后，重构用户的问题
        """
        logger.info("------- user_question_refactor_node -------")
        if len(state.messages) <= 2:  # 若历史聊天记录就1~2条则不重构
            refactored_question = state.refactored_question
        else:
            if state.selected_history is not None:
                history_messages = state.selected_history.dict()
            else:
                history_messages = HousekeeperAgentBase.format_history_messages(
                    state.messages, 10
                )
            refactor_prompt = get_langgraph_prod_prompt(
                self.prompt_names["refactor_user_question"],
                history_messages=history_messages,
            )
            refactored_question = self.llm_assistant.invoke(
                refactor_prompt,
                name="refactor_user_question",
            ).content
            state.refactored_question = str(refactored_question).strip()
        return Command(
            goto="conversation_analysis_node",
            update={"refactored_question": refactored_question},
        )

    @observe
    def conversation_analysis_node(
        self,
        state: HousekeeperState,
    ) -> Command[
        Literal[
            "entity_recognition_node",
            "simple_answer_node",
            "search_video_and_reply_node",
        ]
    ]:
        """分析用户的历史对话，判断下一步动作。不同助手一般该节点的逻辑不同"""
        prompt = get_langgraph_prod_prompt(
            self.prompt_names["analyze_previous_conversation"],
            selected_history=state.selected_history.dict(),  # type: ignore
            user_question=state.refactored_question,
        )
        response = self.llm_assistant.with_structured_output(
            self.MessageAnalysisResponse
        ).invoke([HumanMessage(prompt)], name="analyze_previous_conversation")
        if response.任务规划:  # type: ignore
            next_node = "entity_recognition_node"
        elif response.仅搜索视频:  # type: ignore
            next_node = "search_video_and_reply_node"
        else:
            next_node = "simple_answer_node"
        return Command(goto=next_node)

    @staticmethod
    def simple_on_chunk(chunk: str, state: HousekeeperState):
        """简单回答的 on_chunk 函数，这里是限流之后的 chunk 流"""
        # 填充 final_answer_str，作为气泡中的内容预览
        progress_data: HousekeeperProgressData = state.progress_data()
        progress_data.final_answer_str += chunk

    def simple_answer_node(
        self, state: HousekeeperState
    ) -> Command[Literal["summarize_answer"]]:
        """直接对用户的问题进行回答"""
        logger.info("------- simple_answer_node -------")
        if state.assistant_name == AssistantName.content_assistant:
            prompt = get_langgraph_prod_prompt("content_assistant_v4_simple_answer")
        else:
            prompt = get_langgraph_prod_prompt("housekeeper_simple_answer")
        messages = [SystemMessage(prompt)] + state.messages[-5:]

        limiter = AccumulateRateLimiter(0.5)
        # 生成章节内容
        for chunk in self.llm_assistant.stream(
            messages,
            name="simple_answer_node",
        ):
            limiter.append(chunk.content)
            text = limiter.get()
            if text is not None:
                self.simple_on_chunk(text, state)
        value = limiter.get(flush=True)
        if value is not None:
            self.simple_on_chunk(value, state)
        return Command(goto="__end__")

    def search_video_and_reply_node(self, state: HousekeeperState):
        """搜索视频并回复"""
        logger.info("------- search_video_and_reply_node -------")
        # 调用大模型生成视频查询参数
        prompt = get_langgraph_prod_prompt("housekeeper_generate_video_search_params")
        messages = [SystemMessage(prompt)] + state.messages
        response: VideoSearchParams = self.llm_assistant.with_structured_output(
            VideoSearchParams
        ).invoke(
            messages,
            name="generate_video_search_params",
        )  # type: ignore
        # 查询视频，并更新视频到state
        command = search_video.invoke(
            input={
                "query": response.content,
                "tags": response.tags if response.tags else [],
                "author_name": response.author_name,
                "intention": response.intention,
                "category": response.category,
                "sorts": [o.field for o in response.sort] if response.sort else [],
                "source": sns_platform_to_data_platform_map[state.sns_platform],
                "max_cnt": response.limit,
                "tool_call_id": str(state.plan_step_index),
                "conversation_id": state.conversation_id,
                "thread_id": state.thread_id,
                "housekeeper_step_index": 100,  # 不更新progress
            }
        )
        state.searched_videos = command.update["videos"]
        state.videos = command.update["videos"]
        progress_data: HousekeeperProgressData = state.progress_data()
        progress_data.final_video_list = state.searched_videos
        # 生成回复内容
        video_content = state.videos[:N_VIDEO_SEARCH_RESULT]
        messages += [SystemMessage(f"搜索到的视频如下:\n\n {video_content}")]
        limiter = AccumulateRateLimiter(0.5)
        reply_prompt = get_langgraph_prod_prompt(
            "housekeeper_search_video_and_reply",
            formatted_history_messages=self.format_history_messages(state.messages, 4),
            video_content=video_content,
        )
        for chunk in self.llm_assistant.stream(
            reply_prompt,
            name="search_video_and_reply_node",
        ):
            limiter.append(chunk.content)
            text = limiter.get()
            if text is not None:
                self.simple_on_chunk(text, state)
        value = limiter.get(flush=True)
        if value is not None:
            self.simple_on_chunk(value, state)
        return Command(goto="__end__", update=state)

    @observe
    def entity_recognition_node(
        self,
        state: HousekeeperState,
    ) -> Dict:
        """"""
        logger.info("------- entity_recognition_node -------")
        at_list = state.content_json.get("at_list", []) if state.content_json else []
        entity_result = self.entity_recognition(state.refactored_question, at_list)
        return {
            "entity_result": entity_result,
        }

    @observe
    def process_category_node(self, state: HousekeeperState) -> Command:
        """"""
        entity_result = state.entity_result

        entity_result.product_category = query_product_category(
            entity_result.product_category
        )

        at_list = state.content_json.get("at_list", []) if state.content_json else []
        has_at_shop = any(
            at_entity.get("type") == NamedEntityType.SHOP.value for at_entity in at_list
        )

        if not has_at_shop and NamedEntityType.SHOP in entity_result.entity_dict_v2:
            entity_result.entity_dict_v2[NamedEntityType.SHOP] = select_shop(
                entity_result.llm_shop_match
            )

        # 检查是否是白名单的 category
        user: UserVo = get_user(state.user_id)  # pyright: ignore [reportAssignmentType]
        if not get_whitelist_checker().is_valid_product_category(
            user.phone, entity_result.product_category.category1
        ):
            progress_data: HousekeeperProgressData = state.progress_data()  # type: ignore
            progress_data.final_answer_str = (
                "非常抱歉，您无法访问该品类数据。如有疑问，请联系客服。"
            )
            return Command(
                goto="__end__",
                update={
                    "messages": [AIMessage(content=progress_data.final_answer_str)],
                },
            )
        else:
            return Command(
                goto="plan_node",
                update={
                    "entity_result": entity_result,
                },
            )

    @staticmethod
    def do_plan(
        question: str,
        llm_a: ChatOpenAI,
        llm_b: ChatOpenAI,
        prompt_name_a: str,
        prompt_name_b: str,
    ) -> tuple[TaskPlanningResponse, str, str]:
        """
        执行任务规划，返回计划和原始文本
        作为staticmethod，方便在dataset测试时调用，确保测试与线上逻辑一致
        """
        # 任务规划核心模型，用来生成"章节规划部分"，a部分用来生成所需模块，b部分基于模块生成章节规划
        plan_prompt_a = get_langgraph_prod_prompt(prompt_name_a, user_question=question)
        plan_raw_a = llm_a.invoke(
            [HumanMessage(plan_prompt_a)], name="do_plan_a"
        ).content.strip()  # type: ignore
        planned_modules_a = json.loads(plan_raw_a)

        plan_prompt_b = get_langgraph_prod_prompt(
            prompt_name_b,
            user_question=question,
            planned_modules=json.dumps(planned_modules_a, ensure_ascii=False),
        )
        plan_raw_b = llm_b.invoke(
            [HumanMessage(plan_prompt_b)], name="do_plan_b"
        ).content.strip()  # type: ignore
        plan0: TaskPlanningResponse = TaskPlanningResponse.model_validate_json(
            plan_raw_b
        )
        return plan0, plan_raw_a, plan_raw_b

    def assign_video_ranking_module_id_to_dependent_modules(
        self, plan0: TaskPlanningResponse, user_question: str
    ) -> TaskPlanningResponse:
        """
        为依赖于视频榜单的模块分配对应的视频榜单id
        """
        # 为视频榜单模块分配id
        dependent_modules = ["视频分析", "视频选题", "视频评论分析", "视频脚本"]
        count_dependent_modules = 0
        for module_name in dependent_modules:
            count_dependent_modules += plan0.count_module_calls(module_name)
        if plan0.count_module_calls("视频榜单") == 0 or count_dependent_modules == 0:
            return plan0
        elif plan0.count_module_calls("视频榜单") == 1:
            video_ranking_id = ""
            # 先找到唯一的那个视频榜单模块的id
            for chapter in plan0.章节规划:
                if chapter.模块调用 and chapter.模块调用.模块名称 == "视频榜单":
                    video_ranking_id = chapter.模块调用.id
                    break
                if chapter.子章节:
                    for sub_chapter in chapter.子章节:
                        if (
                            sub_chapter.模块调用
                            and sub_chapter.模块调用.模块名称 == "视频榜单"
                        ):
                            video_ranking_id = sub_chapter.模块调用.id
                            break
            # 为需要的模块分配视频榜单id
            for chapter in plan0.章节规划:
                if chapter.模块调用 and chapter.模块调用.模块名称 in dependent_modules:
                    chapter.模块调用.关联视频榜单id = video_ranking_id
                if chapter.子章节:
                    for sub_chapter in chapter.子章节:
                        if (
                            sub_chapter.模块调用
                            and sub_chapter.模块调用.模块名称 in dependent_modules
                        ):
                            sub_chapter.模块调用.关联视频榜单id = video_ranking_id
            return plan0
        else:
            prompt = get_langgraph_prod_prompt(
                "assign_video_ranking_module_id_to_dependent_modules",
                user_question=user_question,
                dependent_modules=dependent_modules,
                plan_origin=plan0.dict(),
            )
            new_plan0 = self.llm_assistant.with_structured_output(
                TaskPlanningResponse
            ).invoke(
                [HumanMessage(prompt)],
                name="assign_video_ranking_module_id_to_dependent_modules",
            )
            return new_plan0  # type: ignore

    @observe
    def plan_node(
        self,
        state: HousekeeperState,
    ) -> Command[Literal["do_plan_steps"]]:

        # 调用微调之后的模型
        plan_max_retries = 3
        for attempt in range(plan_max_retries):
            try:
                plan0, plan_raw_a, plan_raw_b = self.do_plan(
                    question=state.refactored_question,
                    llm_a=self.llm_plan_a,
                    llm_b=self.llm_plan_b,
                    prompt_name_a=self.prompt_names["task_plan_a"],
                    prompt_name_b=self.prompt_names["task_plan_b"],
                )
                # 为视频榜单模块分配id，以及为依赖于视频榜单的模块分配对应的视频榜单id
                plan0.assign_video_ranking_module_id()
                plan0 = self.assign_video_ranking_module_id_to_dependent_modules(
                    plan0, state.refactored_question
                )
                # 2025-04-27更新，调用模型基于"章节规划"生成文章标题和回答思路，展示在前端，无其他实际作用
                if state.sns_platform == SnsPlatform.DOUYIN:  # 白酒的数据最新到3天前
                    today_minus_n = get_today_minus_n_string(3)
                else:
                    today_minus_n = get_today_minus_n_string(7)
                title_and_think_prompt: str = get_langgraph_prod_prompt(
                    self.prompt_names["task_plan_title_and_think"],
                    user_question=state.refactored_question,
                    chapter_plan=plan_raw_b,
                    today_minus_n=today_minus_n,
                    today=get_today_string(),
                )
                title_and_think_raw: str = self.llm_assistant.invoke(
                    [HumanMessage(title_and_think_prompt)], name="生成标题和回答思路"
                ).content  # type: ignore
                title_and_think: TaskPlanningResponse = (
                    TaskPlanningResponse.model_validate_json(title_and_think_raw)
                )
                # 2025-05-19，若用户的问题超出了数据有效范围，会增加一段额外说明
                if title_and_think.额外说明:
                    plan0.回答思路 = (
                        f"{title_and_think.额外说明}\n\n{title_and_think.回答思路}"
                    )
                else:
                    plan0.回答思路 = title_and_think.回答思路

                if not SIHEGPT_STAGE.is_prod_env():
                    plan0.回答思路 = plan0.回答思路 or ""
                    plan0.回答思路 += (
                        f"\n\n实体识别结果:\n{html.escape(str(state.entity_result))}"
                    )
                plan0.文章标题 = title_and_think.文章标题
                plan0.add_line_break_for_markdown()
                break
            except Exception as e:
                logger.error(f"尝试任务规划 {attempt + 1}/{plan_max_retries} 失败: {e}")
                if attempt < plan_max_retries - 1:
                    continue
                else:
                    logger.error("任务规划所有重试都失败了")
                    raise

        langfuse_context.update_current_observation(metadata={"plan0": plan0})

        plan: PlanWithResults = PlanWithResults(
            **plan0.model_dump(),
            chapter_results=[
                ChapterResult(
                    **chapter_task.model_dump(),
                    chapter_block=None,
                    module_call_result=None,
                )
                for chapter_task in plan0.generate_chapter_task_list()
            ],
        )

        plan.chapter_tasks = plan.generate_chapter_task_list()

        return Command(
            goto="do_plan_steps",
            update={
                "plan": plan,
                "plan_step_index": 0,
            },
        )

    def do_plan_steps(self, state: Union[HousekeeperState, ContentAssistantState]):
        """这里没有实际功能，大多代码在 do_plan_steps_condition"""
        logger.info("------- do_plan_steps -------")
        # 更新UI标头，为重构后的问题
        progress_data: HousekeeperProgressData = state.progress_data()  # type: ignore
        progress_data.progressInfo.chat_header = state.refactored_question
        # 这里生成两个固定的progressInfo，一个是思考链，一个是数据检索过程
        # 新UI：
        # https://cybercore.feishu.cn/docx/MZaJdxLvZoKL4BxMjn3c2GFgnog?openbrd=1&doc_app_id=501&blockId=ZmXPdMEdroAUdsxUIv9cCevYnvg&blockType=whiteboard&blockToken=SZ3awlsvBhLL5dblqexcQSHInuf#ZmXPdMEdroAUdsxUIv9cCevYnvg
        plan: PlanWithResults = state.plan
        progress_data.progressInfo.gen_new_step()
        progress_data.progressInfo.cur_step().step_detail_list.append(
            f"{plan.回答思路}".replace("\\n", "\n")
        )

        ########### 生成章节 ###########
        self.generate_chapters(state)

        # 整理一波搜索结果 (废弃了 score_X 和 sort_artifacts), 把搜索到的所有内容放都放到 docs/videos 字段中，用于生成引用
        state.docs = state.searched_docs
        state.videos = state.searched_videos

        # 给 UI 展示搜索到的文档、视频
        progress_data.final_doc_list = state.searched_docs
        progress_data.final_video_list = state.searched_videos

        # 生成章节后，就认为所有章节都完成了，下一步就是生成结果了 （summarize_answer）
        return {}

    @observe
    def generate_chapters(self, state: Union[HousekeeperState, ContentAssistantState]):
        """
        生成每个章节的内容。
        """
        logger.info("------- _generate_chapters -------")

        # 遍历每个章节任务(理论上可以改成并行，但是这里需要优化)
        for chapter_idx in range(len(state.plan.chapter_tasks)):
            self._generate_one_chapter(state, chapter_idx)

        langfuse_context.update_current_observation(
            output={"chapter_results": state.plan.chapter_results}
        )

    @staticmethod
    def parse_module_description(raw_content) -> dict:
        """
        解析langfuse上关于模块的说明
        """
        d = defaultdict(dict)
        for s in raw_content.split("\n\n"):
            sections = re.split(r"^##\s+", s, flags=re.M)[1:]
            for section in sections:
                title, content = section.split("\n", 1)
                subsections = re.split(r"^###\s+", content, flags=re.M)
                d[title].update(
                    {
                        sub.split("\n", 1)[0]: sub.split("\n", 1)[1].strip()
                        for sub in subsections[1:]
                    }
                )
        return dict(d)

    @staticmethod
    def _get_filter_rule_by_name(
        name: str, state: HousekeeperState, **prompt_params
    ) -> str:
        """
        today_minus_n 是提示词中认为的数据库最新的数据时间
        today_minus_n_minus_seven 部分提示词需要默认查询最新的7天的数据
        today_minus_thirty_n 部分提示词需要默认查询最新的30天的数据
        不同平台的最新数据时间不同，抖音平台的数据最新到3天前（遵循白酒数据的设置）
        """
        if state.sns_platform == SnsPlatform.DOUYIN:  # 白酒的数据最新到3天前
            today_minus_n = get_today_minus_n_string(3)
            today_minus_n_minus_seven = get_today_minus_n_string(10)
            today_minus_thirty_n = get_today_minus_n_string(33)
        else:
            today_minus_n = get_today_minus_n_string(7)
            today_minus_n_minus_seven = get_today_minus_n_string(14)
            today_minus_thirty_n = get_today_minus_n_string(37)
        return get_langgraph_prod_prompt(
            name,
            today=get_today_string(),
            today_minus_n=today_minus_n,
            today_minus_n_minus_seven=today_minus_n_minus_seven,
            today_minus_thirty_n=today_minus_thirty_n,
            **prompt_params,
        )

    @staticmethod
    def _get_matcher_rule_prompt_by_module_call(module_call: str) -> str:
        """
        不同模块有不同的matcher生成规则
        """
        if module_call == "达人查询":
            return get_langgraph_prod_prompt("housekeeper_daren_matcher_rule")
        else:  # 视频类模块都用同一个matcher，其他模块不需要matcher返回了也用不上
            return get_langgraph_prod_prompt("housekeeper_video_matcher_rule")

    def generate_module_call_params(
        self,
        state: HousekeeperState,
        chapter_task: ChapterTask,
        outline: str,
        previous_chapter_content: str,
        chapter_title: str,
        history_error_messages: str,  # 一般是模块执行报错
    ) -> ModuleCallParams:
        """
        生成模块调用参数
        """
        # 删除 chart 图表数据，再限制喂给 llm 的表格长度
        previous_chapter_content = remove_chart_blocks(previous_chapter_content)
        previous_chapter_content = shorten_markdown_tables(
            previous_chapter_content, LLM_LIMIT
        )

        # NER中的分类信息（分类实体），但可能是四级分类全空，代表无分类信息
        product_dynamic_filters = (
            CategoryDataService.get_instance().get_product_dynamic_filter_rules_v2(
                state.entity_result, state.sns_platform, state.refactored_question
            )
        )

        # https://langfuse.caas-prod.sihe6.com/project/cm4j8eh09004pph3tm453njog/prompts/housekeeper_filter_rule
        housekeeper_filter_rule = self._get_filter_rule_by_name(
            "housekeeper_filter_rule",
            state=state,
            dynamic_filters=product_dynamic_filters,
            category_info=state.entity_result.product_category,
        )

        # https://langfuse.caas-prod.sihe6.com/project/cm4j8eh09004pph3tm453njog/prompts/housekeeper_daren_filter_rule
        housekeeper_daren_filter_rule = self._get_filter_rule_by_name(
            "housekeeper_daren_filter_rule",
            state=state,
        )

        # https://langfuse.caas-prod.sihe6.com/project/cm4j8eh09004pph3tm453njog/prompts/housekeeper_video_filter_rule
        housekeeper_video_filter_rule = self._get_filter_rule_by_name(
            "housekeeper_video_filter_rule",
            state=state,
        )

        # https://langfuse.caas-prod.sihe6.com/project/cm4j8eh09004pph3tm453njog/prompts/housekeeper_matcher_rule
        matcher_rule = self._get_matcher_rule_prompt_by_module_call(
            chapter_task.module_call.模块名称 or ""  # type: ignore
        )

        # https://langfuse.caas-prod.sihe6.com/project/cm4j8eh09004pph3tm453njog/prompts/housekeeper_module_description
        module_description_raw = get_langgraph_prod_prompt(
            "housekeeper_module_description",
            housekeeper_filter_rule=housekeeper_filter_rule,
            housekeeper_daren_filter_rule=housekeeper_daren_filter_rule,
            housekeeper_video_filter_rule=housekeeper_video_filter_rule,
            housekeeper_matcher_rule=matcher_rule,
        )
        module_description = self.parse_module_description(module_description_raw)
        module_name = chapter_task.module_call.模块名称 or ""  # type: ignore

        # https://langfuse.caas-prod.sihe6.com/project/cm4j8eh09004pph3tm453njog/prompts/housekeeper_generate_module_call_params
        generate_module_call_params_prompt = get_langgraph_prod_prompt(
            "housekeeper_generate_module_call_params",
            user_question=state.refactored_question,
            outline=outline,
            previous_chapter_content=previous_chapter_content,
            chapter_title=chapter_title,
            module_name=module_name,
            module_function=module_description[module_name]["模块说明"],
            module_params_rule=module_description[module_name]["模块参数规则"],
            entity_result=state.entity_result.get_entity_dict_yaml(),
            module_params_json_example=module_description[module_name]["示例输出"],
            history_error_messages=history_error_messages,
            sns_platform_prompt=sns_platform_prompt(state.sns_platform),
            today=get_today_string(),
        )

        ##############################################################
        # 生成模块参数
        ##############################################################

        # 为避免生成模块参数时出现不合规的json，最多尝试generate_max_retries次
        generate_max_retries = 3
        module_call_params: ModuleCallParams = ModuleCallParams()

        # React 风格的错误处理和思考过程：每次AI生成的参数、程序检查出来的错误都会被记录于messages中
        messages: list[BaseMessage] = [
            SystemMessage(content=generate_module_call_params_prompt),
        ]

        for attempt in range(generate_max_retries):
            try:
                # 查找对应的章节元数据
                chapter = None
                for chapter_meta in ALL_PRODUCT_MODULES:
                    if chapter_meta.name == str(
                        module_name
                    ) or chapter_meta.name_en == str(module_name):
                        chapter = chapter_meta
                        break

                if chapter is None:
                    raise ValueError(f"未找到模块: {module_name}")
                # 视频主题相关的模块参数，生成逻辑是这个
                if chapter.custom_param_type is not None:
                    llm_param_class = chapter.custom_param_type
                    llm_param: (
                        ContentModuleLlmParams
                    ) = llm_generate_module_call_params.with_structured_output(
                        llm_param_class
                    ).invoke(  # pyright: ignore [reportAssignmentType]
                        messages,
                        name="generate_module_call_params-" + str(module_name),
                    )
                    module_call_params.custom_params = llm_param.to_custom_params(
                        state.entity_result.product_category, state.sns_platform
                    )
                    # 对于视频主题相关模块，不走下面的filter、验证等流程
                    # TODO: 也许不同的模块，都要写自己的 validate
                    break

                json_raw = llm_generate_module_call_params.invoke(
                    messages,
                    name=f"generate_module_call_params-{module_name}-{attempt}",
                ).content
                module_call_params: ModuleCallParams = (
                    ModuleCallParams.model_validate_json(str(json_raw))
                )

                messages.append(AIMessage(content=json_raw))

                # 验证模块参数
                module_call_params.normalize_and_validate(
                    attempt, state.entity_result.product_category
                )

                break
            except Exception as e:
                logger.error(
                    f"尝试生成模块参数 {attempt + 1}/{generate_max_retries} 失败: {e}"
                )
                logger.error(traceback.format_exc())
                messages.append(
                    SystemMessage(
                        content=f"尝试生成模块参数 {attempt + 1}/{generate_max_retries} 失败: {e}"
                    )
                )
                langfuse_context.update_current_observation(
                    metadata={
                        "生成模块错误信息-attempt" + str(attempt): str(e),
                    }
                )

                if attempt < generate_max_retries - 1:
                    continue
                else:
                    logger.error("尝试生成模块参数所有重试都失败了")
                    module_call_params = ModuleCallParams()
        return module_call_params

    @staticmethod
    def replay_generate_module_call_params(
        messages: list[BaseMessage], module_name: str
    ) -> str:
        """重放模块参数生成，这里只返回请求 llm 的结果，不做其他处理"""
        # 查找对应的章节元数据
        chapter = None
        for chapter_meta in ALL_PRODUCT_MODULES:
            if chapter_meta.name == str(module_name) or chapter_meta.name_en == str(
                module_name
            ):
                chapter = chapter_meta
                break

        if chapter is None:
            raise ValueError(f"未找到模块: {module_name}")
        # 视频主题相关的模块参数，生成逻辑是这个
        if chapter.custom_param_type is not None:
            llm_param_class = chapter.custom_param_type
            llm_param: (
                ContentModuleLlmParams
            ) = llm_generate_module_call_params.with_structured_output(
                llm_param_class
            ).invoke(  # pyright: ignore [reportAssignmentType]
                messages,
                name="generate_module_call_params-" + str(module_name),
            )
            return llm_param.model_dump_json()
        else:
            json_raw = llm_generate_module_call_params.invoke(
                messages,
                name=f"generate_module_call_params-{module_name}",
            ).content
            return json_raw  # type: ignore

    def do_module_call(
        self,
        state: Union[HousekeeperState, ContentAssistantState],
        chapter_task: ChapterTask,
        module_call_params: ModuleCallParams,
        progress_data: HousekeeperProgressData,
        outline: str,
        previous_chapter_content: str,
        chapter_title: str,
        today: str,
    ) -> ModuleCallResult:
        return self._do_module_call(
            state=state,
            chapter_task=chapter_task,
            module_call_params=module_call_params,
            progress_data=progress_data,
            outline=outline,
            previous_chapter_content=previous_chapter_content,
            chapter_title=chapter_title,
            today=today,
            module_call_method=generate_chapter_content,
        )

    def _do_module_call(
        self,
        state: Union[HousekeeperState, ContentAssistantState],
        chapter_task: ChapterTask,
        module_call_params: ModuleCallParams,
        progress_data: HousekeeperProgressData,
        outline: str,
        previous_chapter_content: str,
        chapter_title: str,
        today: str,
        module_call_method,
    ) -> ModuleCallResult:
        """
        调用具体的模块得到章节内容str
        """
        module_call_result = ModuleCallResult()
        if chapter_task.module_call.模块名称 in ["视频拆解", "视频仿拍", "图文拆解", "提取视频台词", "生成视频分镜脚本"]:  # type: ignore
            # 检查state是否含有视频或图文信息
            content_json = getattr(state, "content_json", None)
            video_info_list = getattr(state, "video_info", [])
            article_info_list = getattr(state, "article_info", [])
            disassemble_tasks = getattr(state, "disassemble_tasks", [])
            imitate_tasks = getattr(state, "imitate_tasks", [])
            if disassemble_tasks or imitate_tasks:
                filters = []
                matchers = []
                # 设置模块调用参数
                content_module_params = ContentModuleParams(
                    sns_platform=state.sns_platform,
                    filters=filters,
                    dimensions=[],
                    matchers=matchers,
                    user_questions=state.refactored_question,
                    outline=outline,
                    previous_chapter_content=previous_chapter_content,
                    chapter_title=chapter_title,
                    content_json=content_json,
                    user_id=state.user_id,
                    conversation_id=state.conversation_id,
                    thread_id=state.thread_id,
                    custom_params=disassemble_tasks[-1],
                    state=state,
                    chapter_task=chapter_task,
                )
                # 调用内容拆解模块
                module_call_response = generate_chapter_content(
                    chapter_id=chapter_task.module_call.模块名称,  # type: ignore
                    params=content_module_params,
                    thread_id=state.thread_id,
                )

                if module_call_response.success:
                    module_call_result.content = module_call_response.text
                else:
                    module_call_result.success = False
                    module_call_result.fail_message = module_call_response.error_message
            else:
                # 用于内容助手的模块，没有视频信息时只提示用户
                fixed_content_module_dict = json.loads(
                    get_langgraph_prod_prompt(
                        "content_assistant_v4_fixed_content_module"
                    )
                )
                module_call_result.content = f"{chapter_title}\n\n{fixed_content_module_dict[chapter_task.module_call.模块名称]}"  # type: ignore
                module_call_result.success = True
        elif chapter_task.module_call.模块名称 in ["研报查询", "研报搜索"]:  # type: ignore
            command = retrieve.invoke(
                input={
                    "query": module_call_params.research_report_search_keywords,
                    "report_type": "research_report",
                    "tool_call_id": str(state.plan_step_index),
                    "thread_id": state.thread_id,
                    "housekeeper_step_index": 100,  # 不更新progress
                }
            )
            # 搜到内容更新到state中
            state.searched_docs = command.update["docs"]
            progress_data.progressInfo.cur_step().step_detail_list.append(
                f"""已检索到{len(command.update['docs'])}条研报。"""
            )
            # 调用一下大模型
            research_paper_prompt = get_langgraph_prod_prompt(
                "housekeeper_research_paper_summary",
                user_question=state.refactored_question,
                outline=outline,
                previous_chapter_content=previous_chapter_content,
                chapter_title=chapter_title,
                research_paper_content=command.update["docs"],
                today=today,
            )
            generated_content = self.llm_assistant.invoke(
                research_paper_prompt,
                name="generate_research_paper_chapter",
            ).content
            module_call_result.content = str(generated_content)
        elif chapter_task.module_call.模块名称 == "视频搜索":  # type: ignore
            video_params = module_call_params.video_search_params
            if video_params is not None:
                command = search_video.invoke(
                    input={
                        "query": (
                            video_params.content
                            if hasattr(video_params, "content")
                            else ""
                        ),
                        "tags": (
                            video_params.tags
                            if hasattr(video_params, "tags") and video_params.tags
                            else []
                        ),
                        "intention": (
                            video_params.intention
                            if hasattr(video_params, "intention")
                            else 0
                        ),
                        "category": (
                            video_params.intention
                            if hasattr(video_params, "category")
                            else []
                        ),
                        "author_name": (
                            video_params.author_name
                            if hasattr(video_params, "author_name")
                            else ""
                        ),
                        "sorts": (
                            [o.field for o in video_params.sort]
                            if hasattr(video_params, "sort") and video_params.sort
                            else []
                        ),
                        "source": sns_platform_to_data_platform_map[state.sns_platform],
                        "max_cnt": (
                            video_params.limit if hasattr(video_params, "limit") else 10
                        ),
                        "tool_call_id": str(state.plan_step_index),
                        "conversation_id": state.conversation_id,
                        "thread_id": state.thread_id,
                        "housekeeper_step_index": 100,  # 不更新progress
                    }
                )
                # 搜到内容更新到state中
                state.searched_videos = command.update["videos"]
                progress_data.progressInfo.cur_step().step_detail_list.append(
                    f"已检索到{len(command.update['videos'])}条视频。"
                )
                # 从视频搜索结果中，选择前 N 条发送给大模型
                video_content = command.update["videos"][:N_VIDEO_SEARCH_RESULT]
                video_search_prompt = get_langgraph_prod_prompt(
                    "housekeeper_video_summary",
                    user_question=state.refactored_question,
                    outline=outline,
                    previous_chapter_content=previous_chapter_content,
                    chapter_title=chapter_title,
                    video_content=video_content,
                    today=today,
                )
                generated_content = self.llm_assistant.invoke(
                    video_search_prompt,
                    name="generate_video_search_chapter",
                ).content
                module_call_result.content = str(generated_content)
            else:
                module_call_result.success = False
                module_call_result.fail_message = "无法处理搜索参数"
        else:
            # 构建matcher，用作在搜索引擎中做匹配
            matchers = []
            if module_call_params.matcher:
                matcher = module_call_params.matcher
                if isinstance(matcher, DarenMatcher):
                    if matcher.tags:
                        matchers.append(Matcher(name="tags", value=matcher.tags or []))
                    if matcher.bring_product_brand:
                        matchers.append(
                            Matcher(
                                name="bring_product_brand",
                                value=matcher.bring_product_brand or [],
                            )
                        )
                    if matcher.bring_product_category:
                        matchers.append(
                            Matcher(
                                name="bring_product_category",
                                value=matcher.bring_product_category or [],
                            )
                        )
                    if matcher.fans_profile_info:
                        matchers.append(
                            Matcher(
                                name="fans_profile_info",
                                value=matcher.fans_profile_info or [],
                            )
                        )
                    if matcher.live_profile_info:
                        matchers.append(
                            Matcher(
                                name="live_profile_info",
                                value=matcher.live_profile_info or [],
                            )
                        )
                    if matcher.video_profile_info:
                        matchers.append(
                            Matcher(
                                name="video_profile_info",
                                value=matcher.video_profile_info or [],
                            )
                        )
                if isinstance(matcher, VideoMatcher):
                    # query 不存在时，尝试用 topic 代替，如果也没有再用空字符串
                    query_value = matcher.query or matcher.topic or ""
                    matchers.append(Matcher(name="query", value=query_value))
                    if matcher.tags:
                        matchers.append(Matcher(name="tags", value=matcher.tags or []))
                    if matcher.topic:
                        matchers.append(
                            Matcher(name="topic", value=matcher.topic or "")
                        )
                    if matcher.lines:
                        matchers.append(
                            Matcher(name="lines", value=matcher.lines or "")
                        )
                    if matcher.sorts:
                        matchers.append(
                            Matcher(name="sorts", value=matcher.sorts or [])
                        )

            def convert_filters(
                llm_filters: Optional[List[LLMGenFilter]],
            ) -> List[Filter]:
                if llm_filters is None:
                    return []
                tmp_filters = []
                for item in llm_filters:
                    op = (
                        FilterOperator.get_enum(item.operator)
                        if item.operator
                        else FilterOperator.EQ
                    )
                    assert op is not None
                    tmp_filters.append(Filter(item.name, item.value, op))
                return tmp_filters

            filters = convert_filters(module_call_params.filters)

            dimensions = []
            if module_call_params.group_by and not DimensionInfo.in_blacklist(
                module_call_params.group_by
            ):
                # 如果该字段有 , 则只取第一个（目前只支持一个维度）
                if "," in module_call_params.group_by:
                    group_by_name = module_call_params.group_by.split(",")[0]
                else:
                    group_by_name = module_call_params.group_by
                # 部分特殊字段需要转换
                group_by_name = filter_name_to_field_map.get(
                    group_by_name, group_by_name
                )
                dimensions = [DimensionInfo(field_name=group_by_name)]
                filter_to_dsl.add_group_limit(dimensions)

            # 获取子模块列表
            if chapter_task.module_call and chapter_task.module_call.子模块:
                sub_modules = [
                    submodule.模块名称 for submodule in chapter_task.module_call.子模块
                ]
            else:
                sub_modules = None

            # 为所有模块添加通用参数
            content_module_params = ContentModuleParams(
                custom_params=module_call_params.custom_params,
                sns_platform=state.sns_platform,
                filters=filters,
                dimensions=dimensions,
                limit=module_call_params.limit,
                matchers=matchers,
                user_questions=state.refactored_question,
                outline=outline,
                previous_chapter_content=previous_chapter_content,
                chapter_title=chapter_title,
                user_id=state.user_id,
                conversation_id=state.conversation_id,
                thread_id=state.thread_id,
                content_json=getattr(state, "content_json", None),
                state=state,
                sub_modules=sub_modules,
                chapter_task=chapter_task,
            )

            # 收集内容模块参数事件
            collect_content_module_params_event(
                state, chapter_task, module_call_params, filters, dimensions
            )

            module_call_response = module_call_method(
                chapter_id=chapter_task.module_call.模块名称 or "",  # type: ignore
                params=content_module_params,
                thread_id=state.thread_id,
            )
            if module_call_response.success:
                module_call_result.content = module_call_response.text
                module_call_result.content_module_result = module_call_response
            else:
                module_call_result.success = False
                module_call_result.fail_message = module_call_response.error_message
            # 如果是视频分析模块，则需要将视频列表添加到state中
            if module_call_response.data.get("video_mark_list"):
                video_mark_list = module_call_response.data["video_mark_list"]
                existing_videos = state.searched_videos
                # 创建一个现有视频的 aweme_id 集合用于快速查找
                existing_aweme_ids = {video["aweme_id"] for video in existing_videos}
                # 只添加不存在的视频
                filtered_video_doc_list = [
                    video
                    for video in video_mark_list
                    if video["aweme_id"] not in existing_aweme_ids
                ]
                videos = filtered_video_doc_list + existing_videos
                # 更新状态
                state.searched_videos = videos
                progress_data.progressInfo.cur_step().step_detail_list.append(
                    f"已检索到{len(videos)}条视频。"
                )
        return module_call_result

    @retry(tries=3, logger=logger)
    def _generate_one_chapter(
        self, state: Union[HousekeeperState, ContentAssistantState], chapter_idx: int
    ):
        """
        生成单个章节的内容。
        """
        chapter_id = state.plan.chapter_tasks[chapter_idx].chapter_id
        chapter_task = state.plan.chapter_tasks[chapter_idx]
        chapter_result = state.plan.chapter_results[chapter_idx]

        def _generate_chapter_title(c: ChapterTask) -> str:
            """
            生成章节标题，如 ### 1.1 产品分析
            """
            return "{} {} {}".format(
                "#" * (c.chapter_id.count(".") + 2), c.chapter_id, c.title
            )

        def _generate_outline(chapter_tasks: list[ChapterTask]) -> str:
            """
            构建章节任务的大纲字符串
            """
            lines = [_generate_chapter_title(chapter) for chapter in chapter_tasks]
            return "\n".join(lines)

        progress_data: HousekeeperProgressData = state.progress_data()  # type: ignore
        progress_data.progressInfo.gen_new_step()

        chapter_title = _generate_chapter_title(chapter_task)
        outline = _generate_outline(state.plan.chapter_tasks)

        previous_chapter_content = self.chapter_blocks_to_markdown(
            [result.chapter_block for result in state.plan.chapter_results][
                :chapter_idx
            ],
            level=CHAPTER_START_LEVEL,
            max_content_length=None,
        )
        # 删除 chart 图表数据，再缩短表格
        previous_chapter_content = remove_chart_blocks(previous_chapter_content)
        previous_chapter_content = shorten_markdown_tables(
            previous_chapter_content, LLM_LIMIT
        )

        today = get_today_string()

        has_child_chapters = any(
            chapter.chapter_id.startswith(chapter_id + ".")
            for chapter in state.plan.chapter_tasks
        )

        if has_child_chapters:
            # 该章节拥有子章节，强制要求不生成内容，只有一个标题。该章节的chapter_tasks（如有）会复制到子章节中来执行
            chapter_result.chapter_block = ChapterBlock(
                content=_generate_chapter_title(chapter_task)
            )
            self.update_state_with_chapter_result(state, is_final=False)
            return

        def _need_module_call() -> bool:
            if not chapter_task.module_call:
                return False

            # 根据 sns_platform 跳过一些模块
            if (
                state.sns_platform != SnsPlatform.DOUYIN
                and chapter_task.module_call.模块名称
                in ["热点话题", "hot_topic", "搜索指数", "search_index"]
            ):
                return False

            return True

        # 调用模块生成章节内容的部分
        if _need_module_call():
            history_error_messages = ""
            # 若生成的参数导致模块调用失败，则会尝试重新生成参数，最多生成3次
            # 若3次都失败，则会放弃调用模块，进入直接生成内容部分
            for attempt_i in range(3):
                # 调用LLM生成模块调用参数，部分模块不需要参数
                if chapter_task.module_call.模块名称 not in [  # type: ignore
                    "视频拆解",
                    "视频仿拍",
                    "图文拆解",
                    "提取视频台词",
                    "生成视频分镜脚本",
                ]:
                    module_call_params: ModuleCallParams = (
                        self.generate_module_call_params(
                            state,
                            chapter_task,
                            outline,
                            previous_chapter_content,
                            chapter_title,
                            history_error_messages,
                        )
                    )
                else:
                    module_call_params = ModuleCallParams()
                history_error_messages += f"第{attempt_i+1}次尝试生成的参数:\n{module_call_params.model_dump_json()}\n"

                # 调用模块的章节直接用模块的调用结果作为内容
                module_call_result = self.do_module_call(
                    state=state,
                    chapter_task=chapter_task,
                    module_call_params=module_call_params,
                    progress_data=progress_data,
                    outline=outline,
                    previous_chapter_content=previous_chapter_content,
                    chapter_title=chapter_title,
                    today=today,
                )
                if module_call_result.success:
                    chapter_result.chapter_block = ChapterBlock(
                        content=str(module_call_result.content)
                    )
                    chapter_result.module_call_result = module_call_result
                    self.update_state_with_chapter_result(state, is_final=False)

                    # 展示思考过程：模块调用标题+描述 (仅当模块调用成功时展示)
                    desc_md = ""
                    if module_call_params.desc_title:
                        desc_md = f"# {module_call_params.desc_title}\n\n{module_call_params.description}"
                    if desc_md:
                        progress_data.progressInfo.cur_step().step_detail_list.append(
                            desc_md
                        )

                    return
                else:
                    history_error_messages += f"第{attempt_i+1}次尝试的错误信息:\n{module_call_result.fail_message}\n\n"
                    # 若3次查询失败，不再进入直接AI生成，直接输出错误（仅在非生产环境输出）
                    if attempt_i == 2 and not SIHEGPT_STAGE.is_prod_env():
                        chapter_result.chapter_block = ChapterBlock(
                            content=f'{chapter_title}\n\n尝试调用模块"{chapter_task.module_call.模块名称}"失败三次，无法生成该章节\n'  # type: ignore
                        )
                        self.update_state_with_chapter_result(state, is_final=False)
                        return

        # 不调用模块直接AI生成内容部分
        progress_data.progressInfo.cur_step().step_detail_list.append(
            f"我正在编写{chapter_task.title}。"
        )

        if state.assistant_name == AssistantName.content_assistant:
            prompt_name = "content_assistant_v4_chapter_content_generation"
        else:
            prompt_name = "housekeeper_chapter_content_generation"

        task_prompt = get_langgraph_prod_prompt(
            prompt_name=prompt_name,
            formatted_history_messages=self.format_history_messages(state.messages, 5),
            user_question=state.refactored_question,
            outline=outline,
            previous_chapter_content=previous_chapter_content,
            chapter_title=_generate_chapter_title(chapter_task),
            today=today,
            sns_platform_prompt=sns_platform_prompt(state.sns_platform),
        )

        messages = [HumanMessage(task_prompt)]
        gen_name = (
            f"generate_one_chapter [({chapter_task.chapter_id} {chapter_task.title})"
        )

        def on_chunk(chunk: str, state: HousekeeperState):
            """这里是限流之后的 chunk 流"""
            # 保存生成的章节内容
            chapter_result.chapter_block.content += chunk  # type: ignore
            self.update_state_with_chapter_result(state, is_final=False)

        # 目前markdown to blocksuite性能很差，限制30秒更新一次
        # XXX: 由于现在的文档回答长度非常大，在更新文档时，doc更新、读写DB等都消耗很大。所以降低频率到30秒一次
        limiter = AccumulateRateLimiter(30)
        chapter_result.chapter_block = ChapterBlock(content="")
        # 生成章节内容
        for chunk in self.llm_assistant.stream(
            messages,
            name=gen_name,
        ):
            limiter.append(chunk.content)
            text = limiter.get()
            if text is not None:
                on_chunk(text, state)
        value = limiter.get(flush=True)
        if value is not None:
            on_chunk(value, state)

        return {"chapter_results": state.plan.chapter_results}

    def get_summary_answer(self, state: HousekeeperState) -> str:
        """
        将回复文章总结成一段简短的回答，观点鲜明
        """
        chapter_content = self.chapter_blocks_to_markdown(
            [result.chapter_block for result in state.plan.chapter_results],
            level=CHAPTER_START_LEVEL,
            max_content_length=None,
        )
        chapter_content = remove_chart_blocks(chapter_content)
        chapter_content = shorten_markdown_tables(chapter_content, LLM_LIMIT)
        user_question = state.refactored_question
        prompt = get_langgraph_prod_prompt(
            "housekeeper_get_summary_answer",
            chapter_content=chapter_content,
            user_question=user_question,
        )
        summary_answer = self.llm_assistant.invoke(
            prompt,
            name="get_summary_answer",
        ).content
        return str(summary_answer)

    def update_state_with_chapter_result(
        self, state: Union[HousekeeperState, ContentAssistantState], is_final: bool
    ) -> str:
        """
        将章节块转换文档，进行填充。回答气泡中附加文档链接
        is_final 为 True 时，代表回答完全结束
        """
        progress_data: HousekeeperProgressData = state.progress_data()  # type: ignore
        if progress_data is None:
            raise ValueError("progress_data is None")
        chapter_blocks = [result.chapter_block for result in state.plan.chapter_results]
        markdown_content = self.chapter_blocks_to_markdown(
            chapter_blocks,
            level=CHAPTER_START_LEVEL,
            max_content_length=None,
        )
        # 对 ref tags 进行处理
        markdown_content = self.ref_tag_to_affine_link(markdown_content, state)
        doc_title = state.plan.文章标题  # type: ignore
        # 这样可保证 Blocksuite 文章带有标题
        markdown_content = f"# {doc_title}\n\n{markdown_content}"

        # 提示：这里可能是新建文档，也可能是更新文档
        new_doc_url, workspace_id, doc_id = document_service.update_md_doc(
            user_id=state.user_id,
            doc_id=progress_data.attached_doc_id,
            markdown_text=markdown_content,
            mark_new_version=False,
        )
        progress_data.attached_doc_id = doc_id
        progress_data.attached_doc = {
            "url": new_doc_url,
            "title": doc_title,
            "raw_content": markdown_content,
        }  # type: ignore
        # 2025.3.31: 新版思考过程UI
        # https://cybercore.feishu.cn/docx/MZaJdxLvZoKL4BxMjn3c2GFgnog?openbrd=1&doc_app_id=501&blockId=ZmXPdMEdroAUdsxUIv9cCevYnvg&blockType=whiteboard&blockToken=SZ3awlsvBhLL5dblqexcQSHInuf#ZmXPdMEdroAUdsxUIv9cCevYnvg
        # 计算最终答案的文字数量 (UI上用) —— 仅统计中文字符
        progress_data.number_of_chars_generated = count_chinese_characters(
            markdown_content
        )

        # 只有housekeeper且is_final时去获取最终总结性的回答，否则为空。不会影响content_assistant的回答
        if is_final and state.assistant_name == AssistantName.housekeeper:
            summary_answer = self.get_summary_answer(state)
            summary_answer = summary_answer.strip() + "\n\n"
        else:
            summary_answer = ""

        if not is_final:
            progress_data.final_answer_str = f"""
正在为您撰写文档...

[{doc_title}]({new_doc_url})

<span style='color: lightgray;'>（{count_chinese_characters(markdown_content)}字）</span><loading-img-1></loading-img-1>
"""
        else:
            progress_data.final_answer_str = f"""
{summary_answer}以下是为您撰写的文档，请点击查看：
[{doc_title}]({new_doc_url})
"""

            langfuse_context.update_current_observation(
                metadata={
                    "最终结果字符数": count_chinese_characters(markdown_content),
                    "最终结果汉字数": count_chinese_characters(markdown_content),
                    "气泡回答汉字数": count_chinese_characters(
                        progress_data.final_answer_str
                    ),
                }
            )
        return markdown_content

    def ref_tag_to_affine_link(self, content: str, state: HousekeeperState):
        pattern = r'<doc_ref ref_id="(.{3})" />'

        def replace_tag(match) -> str:
            docs = state.get("docs", []) + state.get("searched_docs", [])
            ref_id = match.group(1)
            for idx in range(len(docs)):
                doc = docs[idx]
                if doc[DOC_REF_ID] == ref_id:
                    return f"[🔗]({doc['doc_url']})"
            return ""  # Default replacement if no matching ref_id

        content = re.sub(pattern, replace_tag, content)

        content = substitute_table(content, state)
        return remove_video_ref(content)

    @observe
    def summarize_answer(self, state: HousekeeperState):
        """
        做最终的处理（处理引用等）
        """
        markdown_content = self.update_state_with_chapter_result(state, is_final=True)

        progress_data: HousekeeperProgressData = state.progress_data()

        return {
            "messages": [AIMessage(content=progress_data.final_answer_str)],
            "markdown_content": markdown_content,
        }

    def chapter_blocks_to_markdown(
        self,
        chapter_blocks: list[ChapterBlock | None],
        level: int,
        max_content_length: None | int,
    ) -> str:
        """
        将章节块转换为 Markdown 文本。
        :param chapter_blocks: 章节块列表
        :param level: 当前标题级别，起始为 h2
        :return: Markdown 格式的字符串
        """
        SEPARATOR = "\n\n---\n\n"
        paragraphs = []
        for chapter in chapter_blocks:
            if chapter is None or not chapter.content:
                continue
            if max_content_length and len(chapter.content) > max_content_length:
                # 避免截断时，把 html 标签截断
                text = remove_any_html_tag(chapter.content)
                # 删除 --- （分隔线），否则在气泡中有大量分隔线，很难看
                text = text.replace(SEPARATOR, "")
                paragraphs.append(
                    text[:max_content_length]
                    + f"……\n\n<span style='color: lightgray;'>（{len(text)}字）</span>"
                )
            else:
                paragraphs.append(chapter.content)
        if len(paragraphs) > 0:
            # 删除最后一个分隔符
            if paragraphs[-1].endswith(SEPARATOR):
                paragraphs[-1] = paragraphs[-1][: -len(SEPARATOR)]
        markdown = SEPARATOR.join(paragraphs)
        return markdown

    @observe
    def single_content_ref(self, content, user_id) -> dict:
        logger.info("------- single_content_ref -------")

        title = self.llm_assistant.invoke(
            f"请为以下内容生成标题，除标题外，不要回复任何其它内容。\n\n{content}",
            name="generate_affine_title",
        ).content
        content_with_title = f"# {title}\n\n{content}"
        new_link = {
            "url": document_service.import_markdown_doc(
                content_with_title, user_id=user_id
            ),
            "title": title,
            "raw_content": content,
        }
        return new_link

    @observe
    def generate_ref_link(self, attachments: list[str], state: HousekeeperState):
        """
        将附件内容转换为文档链接  (废弃)
        其实现在不会生成其他参考链接了，只会用 progress_data.attached_doc
        """
        new_links = []
        for content in attachments:
            user_id = state.user_id
            # 在文档中，将引用替换为链接
            new_content = self.ref_tag_to_affine_link(content, state)
            # 生成一个标题，上传到用户文档库，生成文档链接
            new_link = self.single_content_ref(new_content, user_id)
            new_links.append(new_link)

        return new_links

    def ask_user(self, question: Question):
        print("询问用户: ")
        print("-", question.question_message)
        for option in question.options:
            print("  *", option)
        ans = question.options[0]
        print("用户回答: ", ans)
        return ans


class EcommerceHousekeeperAgent(HousekeeperAgentBase):
    """
    电商助手，继承自 HousekeeperAgentBase，行为与后者完全相同
    """


# 创建全局实例
ECOMMERCE_HK_INSTANCE = EcommerceHousekeeperAgent()


if __name__ == "__main__":
    # 输出graph结构
    from langchain_core.runnables.graph import MermaidDrawMethod
    import os

    # 下面画图时需要代理进行请求
    # os.environ["HTTPS_PROXY"] = GLOBAL_CONF.LLM_API_PROXY

    print(ECOMMERCE_HK_INSTANCE.housekeeper_graph.get_graph(xray=True).draw_mermaid())
    print("请将上面的mermaid代码复制到 https://mermaid.live/ 中查看")
