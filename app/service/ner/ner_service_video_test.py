from app.category_config import CategoryInfo
from app.service.ner.ner_result_data import NerResult, NamedEntityType, NerResultItem
from app.service.ner.ner_service import get_ner_service
from langfuse.decorators import observe
import pytest
import time

from app.service.ner.ner_service_test import assert_ner_result_has


@observe
def test_ner_showcase():
    question1 = "帮我找一些潮流穿搭的视频"
    result1: NerResult = get_ner_service().ner_video(question1)
    print("test_ner_dedup question", result1)
    assert result1.video_category_v2 == CategoryInfo(
        category1="时尚",
        category2="穿搭",
        category3="",
        category4="",
    )
    assert_ner_result_has(result1, NamedEntityType.DIMENSION_VALUE, "潮流穿搭")


@observe
def test_ner_middle_age_women():
    question1 = "中年女性最喜欢看什么视频"
    result1: NerResult = get_ner_service().ner_video(question1)
    print("test_ner_dedup question", result1)
    assert result1.video_category_v2 == CategoryInfo(
        category1="时尚",
        category2="穿搭",
    )
    assert_ner_result_has(result1, NamedEntityType.DIMENSION_VALUE, "成熟女性")


@observe
def test_ner_hot_word():
    question1 = "近期时令热词有哪些"
    result1: NerResult = get_ner_service().ner_video(question1)
    print("test_ner_dedup question", result1)
    assert result1.video_category_v2 == CategoryInfo(
        category1="时尚",
        category2="穿搭",
    )
    # 来自标注系统：https://cybercore.sihe6.com/labeller/label?id=a94b1e32695e456488daefe74903bb84
    # 时令热词 -> 季节关键词
    assert_ner_result_has(result1, NamedEntityType.DIMENSION_KEY, "季节关键词")


@observe
def test_ner_hot_word_1():
    question1 = "近30天高端女装连衣裙种草视频榜单"
    result1: NerResult = get_ner_service().ner_video(question1)
    print("test_ner_dedup question", result1)
    assert result1.video_category_v2 == CategoryInfo(
        category1="时尚",
        category2="穿搭",
    )
    # 来自标注系统：https://cybercore.sihe6.com/labeller/label?id=a94b1e32695e456488daefe74903bb84
    # 时令热词 -> 季节关键词
    assert_ner_result_has(result1, NamedEntityType.DIMENSION_VALUE, "高端")
