import copy
import pickle
import difflib
import heapq
import json
import os
import re
import threading
import time
import traceback
from collections import defaultdict, OrderedDict
from concurrent.futures import ThreadPoolExecutor

from langchain_core.messages import SystemMessage, HumanMessage
from langfuse.decorators import observe

from app.ch.model import EmbeddingTextType
from app.ch.product_attributes import degree_re
from app.client.openai_client import fast_llm, long_context_llm

from typing_extensions import TypedDict, Optional, List, Dict, Set

import jieba
from pypinyin import Style, pinyin

from app.ch.orm import ChClient
from app.category_config import (
    CategoryInfo,
    create_category_info,
    CategoryWhitelistConfig,
    CategoryMerger,
    is_sub_or_same_category,
)
from app.logger import logger
from app.service.ner.entity_source import (
    BaseEntitySource,
    NamedEntity,
    is_ner_cache_valid,
)
from app.service.ner.entity_source.additional_dimensions_entity_source import (
    AdditionalDimensionsEntitySource,
)
from app.service.ner.entity_source.feishu_entity_source import FeishuEntitySource
from app.service.ner.entity_source.main_entity_source import MainEntitySource
from app.service.ner.entity_source.category_entity_source import (
    VIRTUAL_CATEGORY_DEPTH,
    CategoryEntitySource,
)
from app.service.ner.entity_source.video_entity_source import VideoEntitySource
from app.service.ner.entity_source.video_entity_source_v2 import VideoEntitySourceV2
from app.service.ner.shop_select_branch import ShopSelectInput, invoke_shop_selection
from app.service.ner.word2vector import (
    _get_daren_ne_list,
    normalize,
    Word2Vector,
    _get_shop_ne_list,
    is_entity_too_short,
    init_embedding_text_db,
)
from app.service.search_agent.llm_def import llm_housekeeper
from app.service.ner.ner_result_data import (
    NerResult,
    NamedEntityType,
    EntityGuessResult,
    NerResultItem,
)
from app.service.search_agent.utils import get_today_string
from app.utils.langfuse_utils import get_langgraph_prod_prompt
from app.utils.robot_utils import notify_robot
from app.config import GLOBAL_CONF
from app.env import SIHEGPT_STAGE


def ner_result_add_entity(ner_result: NerResult, entity: NamedEntity):
    if entity.entity_type == NamedEntityType.PRODUCT_CATEGORY:
        ner_result.product_category = list(entity.product_category_set)[0]
        return
    if entity.entity_type == NamedEntityType.VIDEO_CATEGORY:
        # 无需识别视频分类，由 LLM 识别
        return
    if entity.entity_type == NamedEntityType.VIDEO_CATEGORY_V2:
        ner_result.video_category_v2 = list(entity.video_category_v2_set)[0]
        return

    # update v2
    entity_set_v2 = ner_result.entity_dict_v2.get(entity.entity_type, [])
    dimension_key: Optional[str] = None
    description: Optional[str] = None
    if "dimension_key" in entity.attributes:
        dimension_key = entity.attributes["dimension_key"]
    if entity.llm_description:
        description = entity.llm_description
    entity_set_v2.append(
        NerResultItem(
            name=entity.name, dimension_key=dimension_key, description=description
        )
    )
    ner_result.entity_dict_v2[entity.entity_type] = list(set(entity_set_v2))


# 获取全拼或简拼
def get_pinyin(s, first_letter=False):
    style = Style.FIRST_LETTER if first_letter else Style.NORMAL
    return "".join([l[0] for l in pinyin(s, style=style)])


init_event = threading.Event()


# 关于分类的实体源，这个很重要，默认加入
category_entity_source = CategoryEntitySource()
# video_entity_source = VideoEntitySource()
video_entity_source_v2 = VideoEntitySourceV2()

# 默认的实体源
DEFAULT_ENTITY_SOURCES = [
    MainEntitySource(),
    AdditionalDimensionsEntitySource(category_entity_source),
    video_entity_source_v2,
    FeishuEntitySource(),
]


def notify_ner_init_failure(error_message: str, error_traceback: str):
    """
    发送 NER 服务初始化失败的飞书通知
    :param error_message: 错误信息
    :param error_traceback: 错误堆栈信息
    """
    if SIHEGPT_STAGE.name == "DEV":
        logger.warning(f"DEV 环境跳过飞书通知: NER 初始化失败 - {error_message}")
        return

    try:
        content = {
            "title": "⚠️ NER 服务初始化失败",
            "content": [
                [
                    {
                        "tag": "text",
                        "text": "NER 服务初始化线程失败，这会影响实体识别功能，需要立即排查并修复！",
                    },
                ],
                [
                    {"tag": "text", "text": "错误信息: "},
                    {"tag": "text", "text": error_message},
                ],
                [
                    {"tag": "text", "text": "错误堆栈: "},
                    {
                        "tag": "text",
                        "text": (
                            error_traceback[:1000] + "..."
                            if len(error_traceback) > 1000
                            else error_traceback
                        ),
                    },
                ],
                [
                    {"tag": "text", "text": "环境: "},
                    {"tag": "text", "text": SIHEGPT_STAGE.name},
                ],
            ],
        }
        notify_robot(GLOBAL_CONF.WEBHOOK_CHAT_VOTING, content)
        logger.info("已发送 NER 初始化失败的飞书通知")
    except Exception as e:
        logger.error(f"发送 NER 初始化失败飞书通知时出错: {e}")


class NerService:
    # 单例实例
    _instance = None
    _instance_lock = threading.Lock()
    _instance_init_failed = False  # 标记初始化是否失败

    @classmethod
    def get_instance(cls):
        """
        懒加载单例，也可使用 NerService.get_instance() 获取实例
        这样搞是避免python module加载时就初始化，防止 pytest 出现各种问题。
        如果初始化失败，则后续调用直接抛出异常，不再尝试初始化。
        """
        if cls._instance_init_failed:
            # 如果之前初始化失败，直接抛出异常
            raise RuntimeError(
                "NER 服务初始化失败，无法获取 NerService 实例。请检查日志并修复问题。"
            )
        if cls._instance is None:
            with cls._instance_lock:
                if cls._instance is None:
                    try:
                        cls._instance = cls(entity_sources=DEFAULT_ENTITY_SOURCES)
                    except Exception as e:
                        # 标记初始化失败
                        cls._instance_init_failed = True
                        # 再次抛出异常
                        raise
        return cls._instance

    def __init__(self, entity_sources: list[BaseEntitySource]):
        self.ambiguous_dict: dict[str, list[NamedEntity]] = {}

        self.shop_word_vectors = Word2Vector(EmbeddingTextType.ShopName)
        self.daren_word_vectors = Word2Vector(EmbeddingTextType.DarenName)

        self.ch_client = ChClient()
        self.entity_sources = entity_sources
        # 使用线程初始化字典
        self.init_thread = threading.Thread(
            target=self._init_dict,
            name="ner_service_init_dict",
            daemon=True,
        )
        self.init_thread.start()

    def wait_init(self):
        """等待初始化完成"""
        self.init_thread.join()

    def __del__(self):
        # 确保在实例被销毁前等待初始化线程完成
        if hasattr(self, "init_thread") and self.init_thread.is_alive():
            self.init_thread.join()

    def _init_dict(self):
        logger.info("初始化实体字典")
        try:
            init_embedding_text_db()
            logger.info(f"从实体源加载实体字典")
            self._load_ambiguous_dict_from_sources()
            self._load_jieba_dict()

            init_event.set()

        except Exception as e:
            error_message = f"NER 服务初始化失败: {str(e)}"
            error_traceback = traceback.format_exc()
            logger.error(f"{error_message}\n{error_traceback}")

            # 发送飞书通知
            notify_ner_init_failure(error_message, error_traceback)

            # 重新抛出异常，让调用方知道初始化失败
            raise

    def _load_jieba_dict(self):
        """加载jieba分词字典

        注意：cache 并不能加快加载速度，因为jieba.add_word()本身就很快，
        而缓存文件的读写反而增加了开销
        """
        start_time = time.time()

        # 直接添加词到jieba
        for word in self.ambiguous_dict.keys():
            jieba.add_word(word)

        end_time = time.time()
        logger.info(f"jieba字典加载完成 (耗时 {end_time - start_time:.1f} 秒)")

    def _load_ambiguous_dict_from_sources(self):
        start_ts = time.time()
        entity_source_futures = []
        all_entities = []
        with ThreadPoolExecutor(max_workers=8) as executor:
            category_future = executor.submit(category_entity_source.get_entities)
            for entity_source in self.entity_sources:
                entity_source_futures.append(
                    executor.submit(entity_source.get_entities)
                )

            categories = category_future.result()
            all_entities.extend(categories)
            for entity_source_future in entity_source_futures:
                entities = entity_source_future.result()
                all_entities.extend(entities)
        logger.info(
            f"原始字典已加载({len(all_entities)} 个实体) (耗时 {time.time() - start_ts:.1f} 秒)"
        )
        self.ambiguous_dict = self._build_ambiguous_dict_with_cache(all_entities)

        end_ts = time.time()
        logger.info(
            f"实体字典初始化完成 (耗时 {end_ts - start_ts:.1f} 秒)，分类数: {len(categories)}, 自定义词库数：{len(self.ambiguous_dict)}"
        )

    def _build_ambiguous_dict_with_cache(self, all_entities):
        """构建实体字典，支持缓存"""
        ambiguous_dict_cache_file = "/tmp/ambiguous_dict_v9.pkl"

        # 检查缓存是否有效
        if is_ner_cache_valid(ambiguous_dict_cache_file):
            logger.info(f"从缓存加载ambiguous_dict: {ambiguous_dict_cache_file}")
            cached_dict = self._load_ambiguous_dict_from_cache(
                ambiguous_dict_cache_file
            )
            if cached_dict is not None:
                return cached_dict

        # 缓存无效或加载失败，重新构建
        logger.info(f"构建ambiguous_dict并缓存")
        ambiguous_dict = self._build_ambiguous_dict(all_entities)
        self._cache_ambiguous_dict(ambiguous_dict, ambiguous_dict_cache_file)
        return ambiguous_dict

    def _load_ambiguous_dict_from_cache(self, cache_file: str):
        """从缓存文件加载ambiguous_dict"""
        try:
            with open(cache_file, "rb") as f:
                ambiguous_dict = pickle.load(f)
            logger.info(f"ambiguous_dict从缓存加载完成，词库数：{len(ambiguous_dict)}")
            return ambiguous_dict
        except Exception as e:
            logger.warning(f"从缓存加载ambiguous_dict失败: {e}，将重新构建")
            return None

    def _cache_ambiguous_dict(self, ambiguous_dict, cache_file: str):
        """缓存ambiguous_dict"""
        try:
            with open(cache_file, "wb") as f:
                pickle.dump(ambiguous_dict, f)
            logger.info(f"ambiguous_dict已缓存到: {cache_file}")
        except Exception as e:
            logger.warning(f"缓存ambiguous_dict失败: {e}")

    def _build_ambiguous_dict(self, all_entities):
        """构建实体字典，将原始实体进行规范化、拼音扩展，并配置权重"""
        ambiguous_dict = {}

        for entity in all_entities:
            names = []
            for name in [entity.name] + entity.aliases:
                normalized_name = normalize(name)
                if normalized_name == "":
                    # logger.warning(f"实体名称规范化后为空：{e}")
                    continue
                names.append(normalized_name)

            # 根据不同类型设定不同权重
            name_weights = {}
            # 原始名称权重为1.0
            for name in names:
                name_weights[name] = 1.0

            # 拼音权重为0.7
            for name in names:
                pinyin_name = get_pinyin(name)
                # 过滤掉过短的
                if len(pinyin_name) < 5:
                    continue
                name_weights[pinyin_name] = 0.7

            # 添加到实体字典
            for word, weight in name_weights.items():
                entity_with_weight = copy.copy(entity)
                entity_with_weight.weight = weight

                if word in ambiguous_dict:
                    ambiguous_dict[word].append(entity_with_weight)
                else:
                    ambiguous_dict[word] = [entity_with_weight]

        return ambiguous_dict

    def find_closest_entities(
        self,
        inverted_index: Dict[str, Set[NamedEntity]],
        query: str,
        threshold=0.7,
        top_k=5,
    ) -> List[NamedEntity]:
        # 提取查询的n-gram
        candidates = set()
        for i in range(len(query) - 1):
            bi_gram = query[i : i + 2]
            if bi_gram in inverted_index:
                candidates.update(inverted_index[bi_gram])

        for i in range(len(query) - 2):
            tri_gram = query[i : i + 3]
            if tri_gram in inverted_index:
                candidates.update(inverted_index[tri_gram])
        candidate_list = list(candidates)

        # 计算编辑距离并找出最接近的实体
        distances = []
        for idx, candidate in enumerate(candidate_list):
            # 归一化的Damerau-Levenshtein距离，值越大越相似
            dist = difflib.SequenceMatcher(a=query, b=candidate.name).ratio()
            if dist >= threshold:  # 转换为相似度
                heapq.heappush(distances, (dist, idx))

        # 获取前top_k个最近的实体
        closest = []
        while distances and len(closest) < top_k:
            idx = heapq.heappop(distances)[1]
            closest.append(candidate_list[idx])

        return closest

    def _get_related_entities(
        self, inverted_index: Dict[str, Set[NamedEntity]], entity_name: str
    ) -> List[NamedEntity]:
        # 根据编辑距离找寻相似实体
        related_entity_list = self.find_closest_entities(
            inverted_index, entity_name, threshold=0.825, top_k=3
        )
        entity_list = []
        if related_entity_list:
            for tmp_ne in related_entity_list:
                ne = copy.copy(tmp_ne)
                ne.weight = 0.5
                entity_list.append(ne)
        return entity_list

    def ner(self, input_sentence) -> NerResult:
        init_event.wait()
        sentence = normalize(input_sentence)
        results = jieba.lcut_for_search(sentence)

        # sort results by length
        results.sort(key=len, reverse=True)
        long_words = []

        entities: list[NamedEntity] = []
        for word in results:
            used = False
            for long_word in long_words:
                if word in long_word:
                    used = True
                    break
            if used:
                continue
            word_entities = self.ambiguous_dict.get(word, [])
            if len(word_entities) == 0:
                continue
            long_words.append(word)
            entities.extend(word_entities)

        # 临时实体，用于给 LLM 猜测相关其他实体
        tmp_entities = self._dedup_entities(entities)
        # 由于NER只能做精确的实体识别，这里让LLM再做一次实体猜想，再通过编辑距离匹配相似的实体(暂时只匹配达人和店铺)
        # 如'剑南春旗舰店' 上面分词会分为 '剑南春' 和 '旗舰店'，而这时我们需要匹配到 '剑南春官方旗舰店'
        # 此时就需要 LLM 输出 '剑南春旗舰店' 然后 fuzzy_match 为 '剑南春官方旗舰店'
        tmp_ner_result = NerResult()
        for entity in tmp_entities:
            ner_result_add_entity(tmp_ner_result, entity)
        logger.info(
            f"实体识别 LLM 之前的结果，问题: {sentence}, 实体: {tmp_ner_result}"
        )

        entity_guess_prompt = get_langgraph_prod_prompt(
            "entity_guess",
            user_input=input_sentence,
            today=get_today_string(),
            entity_result=tmp_ner_result,
        )
        guess_messages = [
            SystemMessage(entity_guess_prompt),
        ]
        guess_entities: EntityGuessResult = llm_housekeeper.with_structured_output(
            EntityGuessResult
        ).invoke(
            guess_messages, name="entity_guess"
        )  # type: ignore

        logger.info(f"LLM猜想的实体: {guess_entities}")
        if guess_entities:
            for daren in guess_entities.daren_names:
                similar_daren_items = self.daren_word_vectors.find_similar(daren)
                for similar_daren_item in similar_daren_items:
                    daren_name = similar_daren_item[0]
                    ne_list = self.ambiguous_dict.get(daren_name, [])
                    ne_daren = next(
                        (
                            ne
                            for ne in ne_list
                            if ne.entity_type == NamedEntityType.DAREN
                        ),
                        None,
                    )
                    if ne_daren:
                        entities.append(ne_daren)
            for shop in guess_entities.shop_names:
                similar_shop_items = self.shop_word_vectors.find_similar(shop)
                for similar_shop_item in similar_shop_items:
                    shop_name = similar_shop_item[0]
                    ne_list = self.ambiguous_dict.get(shop_name, [])
                    ne_shop = next(
                        (
                            ne
                            for ne in ne_list
                            if ne.entity_type == NamedEntityType.SHOP
                        ),
                        None,
                    )
                    if ne_shop:
                        entities.append(ne_shop)
            for other_name in guess_entities.other_names:
                entities.extend(self.ambiguous_dict.get(other_name, []))

        # 现在有大量重复的维度名被识别出来，比如"产地"，因为每个子分类都有，需要进行去重，保留实际的那个分类
        entities = self._dedup_entities(entities)
        # 去掉内容助手相关的视频分类
        entities = [
            entity
            for entity in entities
            if entity.entity_type != NamedEntityType.VIDEO_CATEGORY_V2
        ]

        logger.info(f"实体识别结果，问题: {sentence}, 实体: {entities}")

        ner_result = NerResult()
        for entity in entities:
            ner_result_add_entity(ner_result, entity)

        shop_entity_list = ner_result.entity_dict_v2.get(NamedEntityType.SHOP, [])
        if shop_entity_list:
            shop_select_input = ShopSelectInput(
                shop_list=[entity.name for entity in shop_entity_list],
                refactored_question=input_sentence,
            )
            ner_result.llm_shop_match = invoke_shop_selection(shop_select_input)

        return ner_result

    def ner_video(self, input_sentence) -> NerResult:
        init_event.wait()
        sentence = normalize(input_sentence)
        results = jieba.lcut_for_search(sentence)

        # sort results by length
        results.sort(key=len, reverse=True)
        long_words = []

        entities: list[NamedEntity] = []
        for word in results:
            used = False
            for long_word in long_words:
                if word in long_word:
                    used = True
                    break
            if used:
                continue
            word_entities = self.ambiguous_dict.get(word, [])
            if len(word_entities) == 0:
                continue
            long_words.append(word)
            entities.extend(word_entities)

        # 临时实体，用于给 LLM 猜测相关其他实体
        tmp_entities = self._dedup_video_entities(entities)
        # 由于NER只能做精确的实体识别，这里让LLM再做一次实体猜想，再通过编辑距离匹配相似的实体(暂时只匹配达人和店铺)
        # 如'剑南春旗舰店' 上面分词会分为 '剑南春' 和 '旗舰店'，而这时我们需要匹配到 '剑南春官方旗舰店'
        # 此时就需要 LLM 输出 '剑南春旗舰店' 然后 fuzzy_match 为 '剑南春官方旗舰店'
        tmp_ner_result = NerResult()
        for entity in tmp_entities:
            ner_result_add_entity(tmp_ner_result, entity)
        logger.info(
            f"实体识别 LLM 之前的结果，问题: {sentence}, 实体: {tmp_ner_result}"
        )

        entity_guess_prompt = get_langgraph_prod_prompt(
            "entity_guess",
            user_input=input_sentence,
            today=get_today_string(),
            entity_result=tmp_ner_result,
        )
        guess_messages = [
            SystemMessage(entity_guess_prompt),
        ]
        guess_entities: EntityGuessResult = llm_housekeeper.with_structured_output(
            EntityGuessResult
        ).invoke(
            guess_messages, name="entity_guess"
        )  # type: ignore

        logger.info(f"LLM猜想的实体: {guess_entities}")
        if guess_entities:
            for daren in guess_entities.daren_names:
                similar_daren_items = self.daren_word_vectors.find_similar(daren)
                for similar_daren_item in similar_daren_items:
                    daren_name = similar_daren_item[0]
                    ne_list = self.ambiguous_dict.get(daren_name, [])
                    ne_daren = next(
                        (
                            ne
                            for ne in ne_list
                            if ne.entity_type == NamedEntityType.DAREN
                        ),
                        None,
                    )
                    if ne_daren:
                        entities.append(ne_daren)
            for other_name in guess_entities.other_names:
                entities.extend(self.ambiguous_dict.get(other_name, []))

        # 现在有大量重复的维度名被识别出来，比如"产地"，因为每个子分类都有，需要进行去重，保留实际的那个分类
        entities = self._dedup_video_entities(entities)

        logger.info(f"实体识别结果，问题: {sentence}, 实体: {entities}")

        ner_result = NerResult()
        for entity in entities:
            ner_result_add_entity(ner_result, entity)

        return ner_result

    def suggest_entities(
        self,
        keyword: str,
        limit: int = 200,
        entity_types: list[NamedEntityType] | None = None,
    ) -> list[NamedEntity]:
        """
        根据关键字提供实体推荐。
        - 优先返回前缀匹配的结果。
        - 其次返回包含匹配的结果，并按关键字在实体名中出现的位置升序排列。
        """
        init_event.wait()
        logger.info(f"suggest_entities called with keyword: '{keyword}'")

        unique_results: OrderedDict[str, NamedEntity] = OrderedDict()
        keyword_lower = keyword.lower()
        logger.info(f"Searching for prefix/contains: '{keyword_lower}'")

        prefix_matches: list[NamedEntity] = []
        contains_matches_with_index: list[tuple[int, NamedEntity]] = []

        for entity_name, entities in self.ambiguous_dict.items():
            lower_entity_name = entity_name.lower()
            if lower_entity_name.startswith(keyword_lower):
                prefix_matches.extend(entities)
            elif keyword_lower in lower_entity_name:
                index = lower_entity_name.find(keyword_lower)
                for entity in entities:
                    contains_matches_with_index.append((index, entity))

        # 按子串出现位置升序排序
        contains_matches_with_index.sort(key=lambda x: x[0])
        sorted_contains_matches = [
            entity for index, entity in contains_matches_with_index
        ]

        logger.info(
            f"Found {len(prefix_matches)} prefix matches and {len(sorted_contains_matches)} contains matches."
        )

        # 合并结果，优先使用前缀匹配，并去重
        for entity in prefix_matches + sorted_contains_matches:
            if entity.name not in unique_results:
                unique_results[entity.name] = entity

        results_before_filter = list(unique_results.values())
        logger.info(
            f"Found {len(results_before_filter)} total results before filtering."
        )

        # 根据指定的实体类型进行过滤
        if entity_types:
            logger.info(f"Filtering results by entity_types: {entity_types}")
            results = [
                entity
                for entity in results_before_filter
                if entity.entity_type in entity_types
            ]
            logger.info(f"Found {len(results)} results after filtering.")
        else:
            results = results_before_filter

        final_results = results[:limit]

        # 批量加载实体属性用于描述文字
        self._batch_enrich_entities(final_results)

        logger.info(f"Returning {len(final_results)} results after limit.")
        return final_results

    @staticmethod
    def _merge_entities_category_info(
        entities: list[NamedEntity], get_category_set
    ) -> CategoryInfo:
        merger = CategoryMerger()
        for entity in entities:
            for category_info in get_category_set(entity):
                merger.add_category_info(category_info, entity.weight)
        return merger.get_category_info()

    @staticmethod
    def _get_fallback_category(
        entities: list[NamedEntity], get_category_set
    ) -> CategoryInfo:
        brand_list = [
            entity
            for entity in entities
            if entity.entity_type == NamedEntityType.BRAND
            and len(get_category_set(entity)) != 0
        ]
        if brand_list:
            return NerService._merge_entities_category_info(
                brand_list, get_category_set
            )
        product_list = [
            entity
            for entity in entities
            if entity.entity_type == NamedEntityType.PRODUCT
            and len(get_category_set(entity)) != 0
        ]
        if product_list:
            return NerService._merge_entities_category_info(
                product_list, get_category_set
            )
        shop_list = [
            entity for entity in entities if entity.entity_type == NamedEntityType.SHOP
        ]
        if shop_list:
            return NerService._merge_entities_category_info(shop_list, get_category_set)

        return CategoryInfo()

    @staticmethod
    def _is_category_related_entity(
        entity: NamedEntity, category_info: CategoryInfo
    ) -> bool:
        # category_info 可以是视频分类，也可以是产品分类
        if (
            len(entity.product_category_set) == 0
            and len(entity.video_category_set) == 0
        ):
            return True  # 没有分类默认相关
        # 允许模糊匹配
        for entity_category in entity.product_category_set:
            if is_sub_or_same_category(
                entity_category, category_info
            ) or is_sub_or_same_category(category_info, entity_category):
                return True
        for entity_category in entity.video_category_set:
            if is_sub_or_same_category(
                entity_category, category_info
            ) or is_sub_or_same_category(category_info, entity_category):
                return True

        return False

    @staticmethod
    def _dedup_entities(entities: list[NamedEntity]) -> list[NamedEntity]:
        """由于一个名词可能是分类名、维度值等，需要进行科学的去重
        要求如下：
        1. 分类为最高优先级。如果一个实体已经是分类名，则删除所有其他同名的对象
        2. 对于 dimension value 和 key，其 metadata 均记录着所属分类。如果识别到分类了，则排除掉不属于该分类的 dimension value 和 key
        """
        # 先识别分类实体
        product_category_entity: NamedEntity | None = None
        video_category_set: Set[CategoryInfo] = set()
        for entity in entities:
            if entity.entity_type == NamedEntityType.PRODUCT_CATEGORY:
                if product_category_entity is None:
                    product_category_entity = entity
                else:
                    # 挑depth大的作为最终分类
                    if (
                        entity.attributes["depth"]
                        > product_category_entity.attributes["depth"]
                    ):
                        product_category_entity = entity
            if entity.entity_type == NamedEntityType.VIDEO_CATEGORY:
                # 视频如果有多个分类，随机选一个
                video_category_set.update(entity.video_category_set)

        if product_category_entity is None:
            # fallback，则从整体识别结果（品牌、商品、店铺）推导分类
            category_info = NerService._get_fallback_category(
                entities, lambda x: x.product_category_set
            )
            if category_info.category4:
                product_category_entity = category_entity_source.category_by_name[
                    category_info.category4
                ]
            elif category_info.category3:
                product_category_entity = category_entity_source.category_by_name[
                    category_info.category3
                ]
            elif category_info.category2:
                product_category_entity = category_entity_source.category_by_name[
                    category_info.category2
                ]
            elif category_info.category1:
                product_category_entity = category_entity_source.category_by_name[
                    category_info.category1
                ]

        if len(video_category_set) == 0:
            for entity in entities:
                video_category_set.update(entity.video_category_set)

        category_name_list = []
        tmp_category_entity = product_category_entity
        while tmp_category_entity:
            category_name_list.append(tmp_category_entity.name)
            tmp_category_entity = tmp_category_entity.attributes.get("parent", None)
        for video_category in video_category_set:
            category_name_list.append(video_category.category1)

        # hack: 由于VIRTUAL_CATEGORY的实体名可能是"生椰拿铁"之类的，我们要转成"咖啡"（父实体）
        if (
            product_category_entity
            and product_category_entity.attributes["depth"] == VIRTUAL_CATEGORY_DEPTH
        ):
            product_category_entity = product_category_entity.attributes["parent"]

        if product_category_entity is not None or len(video_category_set) > 0:
            # 删除与分类名同名的实体 （比如成分=咖啡），以及分类实体（可能有多个）
            entities = [
                entity
                for entity in entities
                if entity.name not in category_name_list
                and entity.entity_type != NamedEntityType.PRODUCT_CATEGORY
                and entity.entity_type != NamedEntityType.VIDEO_CATEGORY
            ]

            product_category_info = None
            if product_category_entity is not None:
                product_category_info = list(
                    product_category_entity.product_category_set
                )[0]

            new_entities = []
            for entity in entities:
                if (
                    product_category_info is not None
                    and NerService._is_category_related_entity(
                        entity, product_category_info
                    )
                ):
                    new_entities.append(entity)
                    continue
                for video_category in video_category_set:
                    if NerService._is_category_related_entity(entity, video_category):
                        new_entities.append(entity)
                        break
            entities = new_entities

            # 最后得把分类实体加进去（之前过滤掉了）
            if product_category_entity is not None:
                entities.append(product_category_entity)
            """
            # 去掉电商助手里老的ner_service，避免搞混
            for video_category in video_category_set:
                entities.append(
                    video_entity_source.category_by_name[video_category.category1]
                )
            """

        # 此时还可能有重复的，比如口味、成分都有同样的值，我尽量保证 (name, dimension_key) 唯一
        dedup_set = set()

        def _check_and_add(entity):
            if (
                entity.entity_type != NamedEntityType.DIMENSION_KEY
                and entity.entity_type != NamedEntityType.DIMENSION_VALUE
            ):
                return True
            if (
                entity.name,
                entity.attributes.get("dimension_key", ""),
            ) not in dedup_set:
                dedup_set.add((entity.name, entity.attributes.get("dimension_key", "")))
                return True
            return False

        entities = [entity for entity in entities if _check_and_add(entity)]
        return entities

    @staticmethod
    def _dedup_video_entities(entities: list[NamedEntity]) -> list[NamedEntity]:
        """由于一个名词可能是分类名、维度值等，需要进行科学的去重
        要求如下：
        1. 分类为最高优先级。如果一个实体已经是分类名，则删除所有其他同名的对象
        2. 对于 dimension value 和 key，其 metadata 均记录着所属分类。如果识别到分类了，则排除掉不属于该分类的 dimension value 和 key
        """
        # 第一版，hard code 视频分类
        entities = [
            entity
            for entity in entities
            if entity.entity_type != NamedEntityType.VIDEO_CATEGORY_V2
        ]
        entities.append(video_entity_source_v2.category_by_name["穿搭"])

        # 仅保留 dimension value 和 key 实体，以及 video_category_v2 实体
        entities = [
            entity
            for entity in entities
            if entity.entity_type
            in [
                NamedEntityType.DIMENSION_VALUE,
                NamedEntityType.DIMENSION_KEY,
                NamedEntityType.VIDEO_CATEGORY_V2,
            ]
        ]

        # 先识别分类实体
        video_category_set: Set[CategoryInfo] = set()
        for entity in entities:
            if entity.entity_type == NamedEntityType.VIDEO_CATEGORY_V2:
                video_category_set.update(entity.video_category_v2_set)

        category_name_list = []
        for video_category in video_category_set:
            for depth in range(1, 5):
                category_name = video_category.get_category_name_by_depth(depth)
                if category_name:
                    category_name_list.append(category_name)

        if len(video_category_set) > 0:
            # 删除与分类名同名的实体 （比如成分=咖啡），以及分类实体（可能有多个）
            entities = [
                entity
                for entity in entities
                if entity.name not in category_name_list
                or entity.entity_type == NamedEntityType.VIDEO_CATEGORY_V2
            ]

            new_entities = []
            for entity in entities:
                for video_category in entity.video_category_v2_set:
                    if video_category in video_category_set:
                        new_entities.append(entity)
                        break

            entities = new_entities

        # 此时还可能有重复的，比如口味、成分都有同样的值，我尽量保证 (name, dimension_key) 唯一
        dedup_set = set()

        def _check_and_add(entity):
            if (
                entity.entity_type != NamedEntityType.DIMENSION_KEY
                and entity.entity_type != NamedEntityType.DIMENSION_VALUE
            ):
                return True
            if (
                entity.name,
                entity.attributes.get("dimension_key", ""),
            ) not in dedup_set:
                dedup_set.add((entity.name, entity.attributes.get("dimension_key", "")))
                return True
            return False

        entities = [entity for entity in entities if _check_and_add(entity)]
        return entities

    def lcut(self, sentence: str) -> list[str]:
        init_event.wait()
        return jieba.lcut(sentence)

    def _batch_enrich_entities(self, entities: list["NamedEntity"]) -> None:
        """将实体按类型分组后批量查询 clickhouse 以补充 attributes， 用于推荐实体时补充描述文字和头像。"""

        # 按类型分组
        type_to_entities: dict[NamedEntityType, list[NamedEntity]] = defaultdict(list)
        for e in entities:
            type_to_entities[e.entity_type].append(e)

        try:
            # 达人批量查询
            if NamedEntityType.DAREN in type_to_entities:
                logger.debug(f"批量查询达人: {type_to_entities[NamedEntityType.DAREN]}")
                names = [e.name for e in type_to_entities[NamedEntityType.DAREN]]
                name_list_sql = ",".join(
                    "'" + name.replace("'", "\\'") + "'" for name in names
                )
                sql = (
                    "SELECT daren_name, daren_id, fans_count_total, data_platform FROM agg_daren_basic_info "
                    f"WHERE daren_name IN ({name_list_sql})"
                )
                for row in self.ch_client.dw_db.select(sql):  # type: ignore
                    for e in type_to_entities[NamedEntityType.DAREN]:
                        if e.name == str(row.daren_name):
                            e.attributes["id"] = str(row.daren_id)
                            e.attributes["follower_count"] = int(row.fans_count_total)
                            if hasattr(row, "data_platform"):
                                e.attributes["data_platform"] = str(row.data_platform)
                logger.debug(
                    f"批量查询达人结果: {type_to_entities[NamedEntityType.DAREN]}"
                )

            # 商品批量查询
            if NamedEntityType.PRODUCT in type_to_entities:
                logger.debug(
                    f"批量查询商品: {type_to_entities[NamedEntityType.PRODUCT]}"
                )
                names = [e.name for e in type_to_entities[NamedEntityType.PRODUCT]]
                name_list_sql = ",".join(
                    "'" + name.replace("'", "\\'") + "'" for name in names
                )
                sql = (
                    "SELECT product_id, product_series, product_name, corrected_product_name, pv_count_30d, data_platform FROM agg_product_base_info "
                    f"WHERE product_series IN ({name_list_sql}) OR product_name IN ({name_list_sql}) OR corrected_product_name IN ({name_list_sql})"
                )
                for row in self.ch_client.dw_db.select(sql):  # type: ignore
                    for e in type_to_entities[NamedEntityType.PRODUCT]:
                        # 名称比较忽略大小写
                        if (
                            e.name.lower()
                            in (
                                str(row.product_series).lower(),
                                str(row.product_name).lower(),
                                str(row.corrected_product_name).lower(),
                            )
                            and "id" not in e.attributes
                        ):
                            e.attributes["id"] = str(row.product_id)
                            e.attributes["monthly_sales"] = int(row.pv_count_30d)
                            if hasattr(row, "data_platform"):
                                e.attributes["data_platform"] = str(row.data_platform)
                logger.debug(
                    f"批量查询商品结果: {type_to_entities[NamedEntityType.PRODUCT]}"
                )

            # 店铺批量查询
            if NamedEntityType.SHOP in type_to_entities:
                logger.debug(f"批量查询店铺: {type_to_entities[NamedEntityType.SHOP]}")
                names = [e.name for e in type_to_entities[NamedEntityType.SHOP]]
                name_list_sql = ",".join(
                    "'" + name.replace("'", "\\'") + "'" for name in names
                )
                sql = (
                    "SELECT shop_id, shop_name, attribution, amount_30d, data_platform FROM dim_shop "
                    f"WHERE shop_name IN ({name_list_sql})"
                )
                for row in self.ch_client.dw_db.select(sql):  # type: ignore
                    logo_url = None
                    try:
                        attr_json = (
                            json.loads(row.attribution) if row.attribution else {}
                        )
                        logo_url = attr_json.get("logo")
                    except Exception:
                        logo_url = None
                    for e in type_to_entities[NamedEntityType.SHOP]:
                        # 名称比较忽略大小写
                        if (
                            e.name.lower() == str(row.shop_name).lower()
                            and "id" not in e.attributes
                        ):
                            e.attributes["id"] = str(row.shop_id)
                            if logo_url:
                                e.attributes["logo"] = logo_url
                            if hasattr(row, "amount_30d"):
                                e.attributes["amount_30d"] = float(row.amount_30d or 0)
                            if hasattr(row, "data_platform"):
                                e.attributes["data_platform"] = str(row.data_platform)
                logger.debug(
                    f"批量查询店铺结果: {type_to_entities[NamedEntityType.SHOP]}"
                )

            # 品牌批量查询
            if NamedEntityType.BRAND in type_to_entities:
                logger.debug(f"批量查询品牌: {type_to_entities[NamedEntityType.BRAND]}")
                names = [e.name for e in type_to_entities[NamedEntityType.BRAND]]
                name_list_sql = ",".join(
                    "'" + name.replace("'", "\\'") + "'" for name in names
                )
                sql = (
                    "SELECT brand_id, brand_name_correct, arrayJoin(category1_list) AS category1, attribution, data_platform, amount_30d FROM dim_brand "
                    f"WHERE brand_name_correct IN ({name_list_sql})"
                )
                for row in self.ch_client.dw_db.select(sql):  # type: ignore
                    logo_url = None
                    try:
                        attr_json = (
                            json.loads(row.attribution) if row.attribution else {}
                        )
                        logo_url = attr_json.get("logo")
                    except Exception:
                        logo_url = None
                    for e in type_to_entities[NamedEntityType.BRAND]:
                        # 名称比较忽略大小写
                        if (
                            e.name.lower() == str(row.brand_name_correct).lower()
                            and "id" not in e.attributes
                        ):
                            e.attributes["id"] = str(row.brand_id)
                            e.attributes["category1"] = row.category1
                            if logo_url:
                                e.attributes["logo"] = logo_url
                            if hasattr(row, "data_platform"):
                                e.attributes["data_platform"] = str(row.data_platform)
                            if hasattr(row, "amount_30d"):
                                e.attributes["amount_30d"] = float(row.amount_30d or 0)
                logger.debug(
                    f"批量查询品牌结果: {type_to_entities[NamedEntityType.BRAND]}"
                )
        except Exception as e:
            logger.warning(f"_batch_enrich_entities error: {e}")


# 用于延迟初始化的函数
def get_ner_service() -> NerService:
    """获取 NerService 实例，延迟加载"""
    return NerService.get_instance()


def ner(sentence) -> NerResult:
    """命名实体识别"""
    return get_ner_service().ner(sentence)


def get_category_info_by_result(ner_result: NerResult) -> CategoryInfo:
    return ner_result.product_category


def cut_sentence(sentences_list: list[str]) -> list[str]:
    stopwords = {
        "a",
        "about",
        "above",
        "after",
        "again",
        "against",
        "all",
        "am",
        "an",
        "and",
        "any",
        "are",
        "as",
        "at",
        "be",
        "because",
        "been",
        "before",
        "being",
        "below",
        "between",
        "both",
        "but",
        "by",
        "could",
        "did",
        "do",
        "does",
        "doing",
        "down",
        "during",
        "each",
        "few",
        "for",
        "from",
        "further",
        "had",
        "has",
        "have",
        "having",
        "he",
        "her",
        "here",
        "hers",
        "herself",
        "him",
        "himself",
        "his",
        "how",
        "i",
        "if",
        "in",
        "into",
        "is",
        "it",
        "its",
        "itself",
        "just",
        "me",
        "more",
        "most",
        "my",
        "myself",
        "no",
        "nor",
        "not",
        "now",
        "of",
        "off",
        "on",
        "once",
        "only",
        "or",
        "other",
        "our",
        "ours",
        "ourselves",
        "out",
        "over",
        "own",
        "same",
        "she",
        "should",
        "so",
        "some",
        "such",
        "than",
        "that",
        "the",
        "their",
        "theirs",
        "them",
        "themselves",
        "then",
        "there",
        "these",
        "they",
        "this",
        "those",
        "through",
        "to",
        "too",
        "under",
        "until",
        "up",
        "very",
        "was",
        "we",
        "were",
        "what",
        "when",
        "where",
        "which",
        "while",
        "who",
        "whom",
        "why",
        "will",
        "with",
        "would",
        "you",
        "your",
        "yours",
        "yourself",
        "yourselves",
        "的",
        "在",
        "了",
        "和",
        "是",
        "就",
        "都",
        "版",
        "型",
        "X",
        "ml",
        " ",
        "\u3000",
    }
    result = []
    for sentence in sentences_list:
        words = get_ner_service().lcut(sentence)
        result.extend(
            [word for word in words if word not in stopwords and len(word) > 1]
        )
    return result
