"""
飞书白名单同步服务
从飞书表格同步用户白名单数据到 ClickHouse
支持增量同步，只有数据变更时才执行同步操作
内置后台线程，每3分钟自动检查并同步
"""

import json
import threading
import time
from datetime import datetime
from typing import List, Dict, Any, Optional

from app.client.feishu_client import FeishuClient, Spreadsheet, Sheet
from app.ch.orm import ChClient
from app.ch.model import UsersWhitelist
from app.db.repository.user_repository import get_user_by_phone
from app.logger import logger


class FeishuWhitelistSyncer:
    """飞书白名单同步器（内置后台线程）"""

    def __init__(self, auto_start: bool = False):
        self.feishu_client = FeishuClient()
        self.ch_client = ChClient()

        # 白名单表格配置（同时同步 staging 和 prod 环境）
        staging_sheet_id = "hd0PgS"  # v2-staging
        prod_sheet_id = "1l6IxS"  # v2-prod

        self.whitelist_spreadsheet = Spreadsheet(
            spreadsheet_token="XbeSs73eAhhjMvtUTHOcPr37nzb",
            sheets=[
                # Staging 环境表格
                Sheet(
                    sheet_id=staging_sheet_id,
                    start_col="A",
                    end_col="H",  # 扩展到H列，包含三个品类和内部用户标识
                    start_row=2,  # 跳过表头
                    end_row=3000,
                ),
                # Prod 环境表格
                Sheet(
                    sheet_id=prod_sheet_id,
                    start_col="A",
                    end_col="H",  # 扩展到H列，包含三个品类和内部用户标识
                    start_row=2,  # 跳过表头
                    end_row=3000,
                ),
            ],
        )

        # 后台线程相关
        self._running = False
        self._thread = None
        self._sync_interval = 180  # 3分钟 = 180秒
        self._last_sync_result = None

        if auto_start:
            self.start_background_sync()

    def fetch_feishu_data(self) -> List[Dict[str, Any]]:
        """从飞书表格获取并解析白名单数据（同时处理 staging 和 prod 环境）"""
        try:
            logger.info("开始从飞书表格获取白名单数据（staging + prod）")

            all_valid_records = []

            # 分别处理每个 sheet（staging 和 prod）
            for i, sheet in enumerate(self.whitelist_spreadsheet.sheets):
                env_name = "staging" if i == 0 else "prod"
                logger.info(f"正在处理 {env_name} 环境的表格数据")

                # 创建单个 sheet 的 Spreadsheet 对象
                single_sheet_spreadsheet = Spreadsheet(
                    spreadsheet_token=self.whitelist_spreadsheet.spreadsheet_token,
                    sheets=[sheet],
                )

                # 获取该 sheet 的数据
                raw_data = self.feishu_client.get_data_from_sheet(
                    single_sheet_spreadsheet
                )
                logger.info(f"从 {env_name} 环境获取到 %d 条原始记录", len(raw_data))

                # 解析数据
                for j, row in enumerate(raw_data):
                    parsed = self.parse_feishu_row(row, env_name)
                    if parsed is None:
                        logger.warning(
                            f"{env_name} 环境第 {j+1} 行数据解析失败，跳过: {row}"
                        )
                        continue
                    all_valid_records.append(parsed)

            logger.info("总共解析得到 %d 条白名单记录", len(all_valid_records))

            # 不进行去重，env_source + phone 组合是唯一的
            return all_valid_records
        except Exception as e:
            logger.error("从飞书获取白名单数据失败: %s", str(e))
            raise

    def parse_feishu_row(
        self, row: List[str], env_name: str = "unknown"
    ) -> Optional[Dict[str, Any]]:
        """
        解析飞书表格行数据

        飞书表格列结构：
        A: 添加日期
        B: 手机号
        C: 客户名称
        D: 添加人
        E: 电商助手品类
        F: 内容助手品类
        G: 阅读助手品类
        H: 是否内部用户（"是" 表示内部用户）
        """
        if not row or len(row) < 4:
            return None
        phone = str(row[1]).strip() if len(row) > 1 and row[1] else ""
        customer_name = row[2].strip() if len(row) > 2 and row[2] else ""
        ecommerce_category = row[4].strip() if len(row) > 4 and row[4] else ""
        content_category = row[5].strip() if len(row) > 5 and row[5] else ""
        reading_category = row[6].strip() if len(row) > 6 and row[6] else ""
        internal_flag = row[7].strip() if len(row) > 7 and row[7] else ""

        # 验证手机号
        if not phone or len(phone) < 11:
            logger.warning("跳过无效手机号记录: %s", row)
            return None

        # 构建扩展信息JSON，包含三个品类
        additional = {
            "ecommerce_category": ecommerce_category,
            "content_category": content_category,
            "reading_category": reading_category,
        }

        # 根据H列判断是否内部人员
        is_internal_user = internal_flag == "是"

        # 通过手机号查询用户ID
        user_id = get_user_by_phone(phone)

        return {
            "phone": phone,
            "customer_name": customer_name,
            "env_source": env_name,  # 数据来源环境
            "user_id": user_id,  # 用户ID，可能为None
            "is_internal_user": is_internal_user,
            "additional": json.dumps(additional, ensure_ascii=False),
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        }

    def get_clickhouse_data(self) -> List[Dict[str, Any]]:
        """从 ClickHouse 获取现有的白名单数据（使用ORM）"""
        try:
            # 使用ORM查询所有白名单数据
            query_result = UsersWhitelist.objects_in(self.ch_client.dw_db).order_by(
                "phone"
            )

            ch_data = []
            for row in query_result:
                ch_data.append(
                    {
                        "phone": row.phone,
                        "customer_name": row.customer_name,
                        "env_source": row.env_source,
                        "user_id": row.user_id,
                        "is_internal_user": row.is_internal_user,
                        "additional": row.additional,
                    }
                )

            logger.info("从 ClickHouse 获取到 %d 条白名单记录", len(ch_data))
            return ch_data

        except Exception as e:
            logger.warning("从 ClickHouse 获取数据失败: %s", str(e))
            return []

    def compare_data(
        self, feishu_records: List[Dict[str, Any]], ch_records: List[Dict[str, Any]]
    ) -> bool:
        """比较飞书数据和 ClickHouse 数据是否一致"""

        # 将数据转换为可比较的格式（排除时间字段）
        def normalize_record(record):
            # 标准化 additional JSON 字段，确保键顺序一致
            additional_str = record.get("additional", "{}")
            try:
                additional_obj = json.loads(additional_str)
                # 重新序列化，确保键顺序一致
                normalized_additional = json.dumps(
                    additional_obj, ensure_ascii=False, sort_keys=True
                )
            except (json.JSONDecodeError, TypeError):
                normalized_additional = "{}"

            return {
                "phone": record.get("phone", ""),
                "customer_name": record.get("customer_name", ""),
                "env_source": record.get("env_source", ""),
                "user_id": record.get("user_id"),
                "is_internal_user": record.get("is_internal_user", False),
                "additional": normalized_additional,
            }

        # 按环境+手机号排序并标准化（因为 env_source + phone 是唯一标识）
        feishu_normalized = sorted(
            [normalize_record(r) for r in feishu_records],
            key=lambda x: (x["env_source"], x["phone"]),
        )
        ch_normalized = sorted(
            [normalize_record(r) for r in ch_records],
            key=lambda x: (x["env_source"], x["phone"]),
        )

        # 比较数据
        is_same = feishu_normalized == ch_normalized

        if is_same:
            logger.info("飞书数据与 ClickHouse 数据一致，无需同步")
        else:
            logger.info(
                "数据不一致 - 飞书: %d 条, ClickHouse: %d 条",
                len(feishu_normalized),
                len(ch_normalized),
            )

            # 添加详细的差异分析
            if len(feishu_normalized) != len(ch_normalized):
                logger.info("记录数量不同")
            else:
                # 找出具体的差异
                for i, (f_record, c_record) in enumerate(
                    zip(feishu_normalized, ch_normalized)
                ):
                    if f_record != c_record:
                        logger.info(f"记录 {i+1} 不同:")
                        logger.info(f"  飞书: {f_record}")
                        logger.info(f"  ClickHouse: {c_record}")
                        break

        return is_same

    def sync_to_clickhouse(self, records: List[Dict[str, Any]]) -> int:
        """将记录同步到 ClickHouse"""
        if not records:
            logger.info("没有需要同步的记录")
            return 0

        try:
            # 创建 UsersWhitelist 对象列表
            whitelist_objects = []
            for record in records:
                whitelist_obj = UsersWhitelist(**record)
                whitelist_objects.append(whitelist_obj)

            # 批量插入到 ClickHouse 数据仓库
            self.ch_client.dw_db.insert(whitelist_objects)
            logger.info("成功同步 %d 条白名单记录到 ClickHouse", len(whitelist_objects))
            return len(whitelist_objects)

        except Exception as e:
            logger.error("同步数据到 ClickHouse 失败: %s", str(e))
            raise

    def sync_atomic(self, records: List[Dict[str, Any]]) -> None:
        """原子性同步：使用临时表和重命名实现"""
        if not records:
            logger.info("没有需要同步的记录")
            return

        timestamp = int(datetime.now().timestamp())
        temp_table_name = f"users_whitelist_temp_{timestamp}"
        backup_table_name = f"users_whitelist_backup_{timestamp}"

        try:
            # 1. 创建临时表（复制表结构，不复制数据）
            self.ch_client.dw_db.raw(
                f"CREATE TABLE datawarehouse.{temp_table_name} AS datawarehouse.users_whitelist"
            )
            logger.info("创建临时表: %s", temp_table_name)

            # 2. 批量插入数据到临时表
            if records:
                # 构建批量插入的VALUES子句
                values_list = []
                for record in records:
                    phone = record["phone"]
                    customer_name = record["customer_name"]
                    env_source = record["env_source"]
                    user_id = record.get("user_id")
                    user_id_str = str(user_id) if user_id is not None else "NULL"
                    is_internal_user = "true" if record["is_internal_user"] else "false"
                    additional = record["additional"]
                    created_at = record["created_at"].strftime("%Y-%m-%d %H:%M:%S")
                    updated_at = record["updated_at"].strftime("%Y-%m-%d %H:%M:%S")

                    values_list.append(
                        f"('{phone}', '{customer_name}', '{env_source}', {user_id_str}, {is_internal_user}, '{additional}', '{created_at}', '{updated_at}')"
                    )

                # 批量插入
                batch_insert_sql = f"""
                INSERT INTO datawarehouse.{temp_table_name} VALUES
                {', '.join(values_list)}
                """
                self.ch_client.dw_db.raw(batch_insert_sql)

                logger.info("成功批量插入 %d 条记录到临时表", len(records))

            # 3. 原子性替换表
            self.ch_client.dw_db.raw(
                f"RENAME TABLE datawarehouse.users_whitelist TO datawarehouse.{backup_table_name}"
            )
            self.ch_client.dw_db.raw(
                f"RENAME TABLE datawarehouse.{temp_table_name} TO datawarehouse.users_whitelist"
            )
            self.ch_client.dw_db.raw(f"DROP TABLE datawarehouse.{backup_table_name}")

            logger.info("原子性同步完成，同步了 %d 条白名单记录", len(records))

        except Exception as e:
            # 清理临时表
            try:
                self.ch_client.dw_db.raw(
                    f"DROP TABLE IF EXISTS datawarehouse.{temp_table_name}"
                )
            except:
                pass
            logger.error("原子性同步失败: %s", str(e))
            raise

    def sync_full(self) -> None:
        """执行全量同步（比较数据后决定是否同步）"""
        logger.info("开始执行飞书白名单同步检查")

        # 1. 获取并解析飞书数据
        valid_records = self.fetch_feishu_data()
        if not valid_records:
            logger.warning("没有有效的白名单记录")
            return

        # 2. 获取 ClickHouse 现有数据
        ch_records = self.get_clickhouse_data()

        # 3. 比较数据是否一致
        if self.compare_data(valid_records, ch_records):
            logger.info("飞书白名单数据一致，跳过同步")
            return

        # 4. 数据不一致，执行全量同步
        logger.info("检测到数据变更，开始全量同步...")
        self.sync_atomic(valid_records)
        logger.info("飞书白名单全量同步完成")

    def start_background_sync(self):
        """启动后台同步线程"""
        if self._running:
            logger.warning("后台同步线程已在运行")
            return

        self._running = True
        self._thread = threading.Thread(target=self._background_sync_loop, daemon=True)
        self._thread.start()
        logger.info(f"飞书白名单后台同步线程已启动，每{self._sync_interval}秒检查一次")

    def stop_background_sync(self):
        """停止后台同步线程"""
        if not self._running:
            return

        self._running = False
        if self._thread and self._thread.is_alive():
            self._thread.join(timeout=5)
        logger.info("飞书白名单后台同步线程已停止")

    def _background_sync_loop(self):
        """后台同步循环"""
        while self._running:
            try:
                logger.debug("执行后台飞书白名单同步检查")
                self.sync_full()
                logger.info("后台同步检查完成")
            except Exception as e:
                logger.error("后台同步线程异常: %s", str(e))

            # 等待下次检查
            for _ in range(self._sync_interval):
                if not self._running:
                    break
                time.sleep(1)

    def get_sync_status(self) -> Dict[str, Any]:
        """获取同步状态"""
        return {
            "running": self._running,
            "sync_interval_seconds": self._sync_interval,
            "thread_alive": self._thread.is_alive() if self._thread else False,
            "last_sync_result": self._last_sync_result,
        }

    def __del__(self):
        """析构函数，确保线程正确停止"""
        self.stop_background_sync()


# 全局同步器实例
_global_syncer = None


def sync_feishu_whitelist() -> None:
    """同步飞书白名单的入口函数（全量同步）"""
    syncer = FeishuWhitelistSyncer()
    syncer.sync_full()


def start_feishu_whitelist_background_sync():
    """启动飞书白名单后台同步服务"""
    global _global_syncer
    if _global_syncer is None:
        _global_syncer = FeishuWhitelistSyncer()
    _global_syncer.start_background_sync()
    return _global_syncer


def stop_feishu_whitelist_background_sync():
    """停止飞书白名单后台同步服务"""
    global _global_syncer
    if _global_syncer:
        _global_syncer.stop_background_sync()


def get_feishu_whitelist_sync_status() -> Dict[str, Any]:
    """获取飞书白名单同步状态"""
    global _global_syncer
    if _global_syncer:
        return _global_syncer.get_sync_status()
    return {"running": False, "message": "同步服务未启动"}


if __name__ == "__main__":
    # 测试同步
    print("=== 手动同步测试 ===")
    syncer = FeishuWhitelistSyncer()
    # data = syncer.fetch_feishu_data()
    # print(f"data: {data}")
    # syncer.sync_full()

    start_feishu_whitelist_background_sync()
