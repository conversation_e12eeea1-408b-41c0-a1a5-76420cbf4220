from fastapi import APIRouter, HTTPException, Response
from fastapi.responses import RedirectResponse
from sqlalchemy import text
import requests, json, re
import os
import threading
from app.logger import logger
from app.db.database import session_scope
from app.client.s3client import S3Client
from app.config import S3_URL_EXPIRATION_TIME
from app.ch.orm import ChClient
from app.config import GLOBAL_CONF

router = APIRouter(prefix="/img/v1", tags=["image-proxy"])

s3_client = S3Client()
# ch_client = ChClient()

# 懒加载 ChClient
_ch_client = None
_ch_client_lock = threading.Lock()


def get_ch_client() -> ChClient:
    """获取 ChClient 实例，使用延迟初始化和线程安全"""
    global _ch_client
    if _ch_client is None:
        with _ch_client_lock:
            if _ch_client is None:
                _ch_client = ChClient()
    return _ch_client


IMAGE_CACHE_PREFIX = "image_cache"  # 存放到 OSS 目录


def _get_requests_proxies():
    """生成 requests 需要的 proxies dict，如果未配置则返回 None"""
    if not GLOBAL_CONF.LLM_API_PROXY:
        return None
    return {
        "http": GLOBAL_CONF.LLM_API_PROXY,
        "https": GLOBAL_CONF.LLM_API_PROXY,
    }


def _build_cache_key(platform: str, res_type: str, res_id: str, ext: str) -> str:
    """生成 OSS Key"""
    return f"{IMAGE_CACHE_PREFIX}/{platform}/{res_type}/{res_id}.{ext}"


def _detect_ext(content_type: str) -> str:
    """根据 Content-Type 推断扩展名，默认 jpg"""
    if content_type.endswith("png"):
        return "png"
    if content_type.endswith("webp"):
        return "webp"
    if content_type.endswith("gif"):
        return "gif"
    if content_type.endswith("jpeg"):
        return "jpeg"
    return "jpg"


def _download_image(url: str, platform: str) -> tuple[bytes, str]:
    """简化版下载图片：支持可选代理，失败直接抛 502"""
    headers = {
        "Accept": "image/avif,image/webp,image/apng,image/*,*/*;q=0.8",
    }
    if platform == "douyin":
        headers["User-Agent"] = (
            "Mozilla/5.0 (iPhone; CPU iPhone OS 13_5 like Mac OS X) "
            "AppleWebKit/605.1.15 (KHTML, like Gecko) "
            "Version/13.1.1 Mobile/15E148 Safari/604.1"
        )
        headers["Referer"] = "https://www.douyin.com/"
    else:
        headers["User-Agent"] = (
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/********* Safari/537.36"
        )
        headers["Referer"] = "https://www.tiktok.com/"

    proxies = _get_requests_proxies() if platform != "douyin" else None

    try:
        resp = requests.get(
            url,
            timeout=30,
            headers=headers,
            proxies=proxies,
        )
        resp.raise_for_status()
        return resp.content, _detect_ext(resp.headers.get("Content-Type", ""))
    except Exception as e:
        logger.warning(f"下载图片失败: {e}, url={url}")
        raise HTTPException(status_code=502, detail="Failed to fetch origin image")


def _safe_json_load(s: str):
    s = s.strip()
    if (s.startswith("'") and s.endswith("'")) or (
        s.startswith('"') and s.endswith('"')
    ):
        s = s[1:-1]
    # fix escaped single quotes
    s = re.sub(r"\\'", "'", s)
    s = s.replace('\\"', '"')
    try:
        return json.loads(s)
    except Exception:
        # replace unescaped backslash before non-escape chars
        s2 = re.sub(r"\\(?![\\\"'/bfnrtu])", r"\\\\", s)
        try:
            return json.loads(s2)
        except Exception:
            logger.warning(f"safe_json_load failed: {s2}")
            return None


def _query_origin_url_douyin_daren(daren_id: str) -> str | None:
    """从clickhouse查询达人头像"""
    did_safe = daren_id.replace("'", "''")
    # 1. 从 datawarehouse.agg_daren_basic_info 表查询 daren_account_id
    sql1 = f"SELECT daren_account_id FROM `agg_daren_basic_info` WHERE daren_id = '{did_safe}' LIMIT 1"
    rows1 = get_ch_client().dw_db.raw(sql1)
    if not rows1:
        return None
    daren_account_id = rows1.strip()
    if not daren_account_id:
        return None

    # 2. 从 mediacrawler.douhot_account_info 表查询 author_info
    sql2 = f"SELECT author_info FROM `douhot_account_info` WHERE sec_uid = '{daren_account_id}' ORDER BY crawl_time DESC LIMIT 1"
    rows2 = get_ch_client().db.raw(sql2)
    if not rows2:
        return None
    # 3. 解析 author_info 获取 avatar_url
    author_info_str = rows2.strip()
    info = _safe_json_load(author_info_str)

    return info.get("avatar_url") if info else None


def _query_origin_url_douyin_shop(shop_id: str) -> str | None:
    """从 dim_shop 表查询店铺头像"""
    sid_safe = shop_id.replace("'", "''")
    sql = f"SELECT attribution FROM `dim_shop` WHERE shop_id = '{sid_safe}' LIMIT 1"
    rows = get_ch_client().dw_db.raw(sql)
    if not rows:
        return None
    attribution_str = rows
    info = _safe_json_load(attribution_str)
    return info.get("logo") if info else None


def _query_origin_url_douyin_product(product_id: str) -> str | None:
    """从 mediacrawler.chanmama_product_detail 表查询商品图片"""
    pid_safe = product_id.replace("'", "''")
    sql = f"SELECT basic_info FROM mediacrawler.chanmama_product_detail WHERE product_id = '{pid_safe}' ORDER BY crawl_time DESC LIMIT 1"
    rows = get_ch_client().db.raw(sql)
    if not rows:
        return None
    basic_info_str = rows
    info = _safe_json_load(basic_info_str)
    if info and info.get("product"):
        return info.get("product", {}).get("image")
    return None


def _query_origin_url_tiktok_product(product_id: str) -> str | None:
    """query mediacrawler.nox_product_detail basic_info.baseInfo.thumbnails[0] by product_id"""
    pid_safe = product_id.replace("'", "''")
    sql = f"SELECT basic_info FROM mediacrawler.nox_product_detail WHERE product_id = '{pid_safe}' ORDER BY crawl_time DESC LIMIT 1"
    rows = get_ch_client().db.raw(sql)
    if not rows:
        return None

    basic_info_str = rows
    info = _safe_json_load(basic_info_str)
    if info:
        thumb_list = info.get("baseInfo", {}).get("thumbnails")
        if thumb_list and isinstance(thumb_list, list):
            return thumb_list[0]
    return None


def _query_origin_url_tiktok_daren(daren_id: str) -> str | None:
    """query mediacrawler.nox_account_detail basic_info.avatar by account_id"""
    did_safe = daren_id.replace("'", "''")
    table2 = f"`{get_ch_client().db.db_name}`.`nox_account_detail`"
    sql = f"SELECT basic_info FROM {table2} WHERE account_id = '{did_safe}' ORDER BY crawl_time DESC LIMIT 1"
    rows = get_ch_client().db.raw(sql)
    if not rows:
        return None

    basic_info_str = rows
    info = _safe_json_load(basic_info_str)
    if info:
        avatar = info.get("avatar") or info.get("basic_data", {}).get("avatar")
        return avatar or None
    return None


@router.get("/platform/{platform}/{res_type}/{res_id}")
def get_image(platform: str, res_type: str, res_id: str):
    """图片查询接口。当前仅支持 platform=tiktok-us/tiktok-au/douyin, res_type=product|daren|shop"""
    platform = platform.lower()
    res_type = res_type.lower()

    if platform not in {"tiktok", "tiktok-us", "tiktok-au", "douyin"}:
        raise HTTPException(status_code=400, detail="unsupported platform")
    if res_type not in {"product", "daren", "shop"}:
        raise HTTPException(status_code=400, detail="unsupported resource type")

    # 先检查缓存
    possible_exts = ["jpg", "png", "jpeg"]
    cached_key = None
    cached_ext = None
    for ext in possible_exts:
        key = _build_cache_key(platform, res_type, res_id, ext)
        if s3_client.check_obj_exists(key):
            cached_key = key
            cached_ext = ext
            break

    if cached_key:
        signed_url = s3_client.get_obj_download_url(
            filepath=cached_key,
            expiration=S3_URL_EXPIRATION_TIME,
            preview=True,
            # content_type a bit tricky for redirect, use mime_type in custom s3 client
            mime_type=f"image/{cached_ext}",
        )
        return RedirectResponse(url=signed_url, status_code=302)

    # 缓存未命中 -> 查数据库，拉取原图
    origin_url: str | None = None
    if platform.startswith("tiktok"):
        if res_type == "product":
            origin_url = _query_origin_url_tiktok_product(res_id)
        elif res_type == "daren":
            origin_url = _query_origin_url_tiktok_daren(res_id)
    elif platform == "douyin":
        if res_type == "daren":
            origin_url = _query_origin_url_douyin_daren(res_id)
        elif res_type == "shop":
            origin_url = _query_origin_url_douyin_shop(res_id)
        elif res_type == "product":
            origin_url = _query_origin_url_douyin_product(res_id)

    if not origin_url:
        raise HTTPException(status_code=404, detail="image origin not found")

    # 下载原图
    img_bytes, ext = _download_image(origin_url, platform)

    # 上传到 OSS
    key = _build_cache_key(platform, res_type, res_id, ext)
    put_ok = s3_client.put_obj(key, img_bytes, content_type=f"image/{ext}")
    if not put_ok:
        raise HTTPException(status_code=500, detail="upload to oss failed")

    signed_url = s3_client.get_obj_download_url(
        filepath=key,
        expiration=S3_URL_EXPIRATION_TIME,
        preview=True,
        mime_type=f"image/{ext}",
    )

    return RedirectResponse(url=signed_url, status_code=302)
