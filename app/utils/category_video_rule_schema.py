from typing import List

from app.category_config import CategoryInfo
from app.utils.category_product_rule_schema import RuleSchema, ShowInPrompt


video_attribute_rules: List[RuleSchema] = [
    RuleSchema(
        key="topic",
        name="视频选题",
        type="string",
        condition=CategoryInfo(),  # 空条件表示通用
        physical_field="JSONExtract(main.attributes, 'topic', 'mapped_value', 'String')",
        ai_description="视频的选题方向，使用中文",
        boost=10,
    ),
    # 以下属性尚未用于 LLM 生成（show_in_prompt=False）
    # 基础标识字段
    RuleSchema(
        key="video_id",
        name="视频ID",
        type="string",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频的唯一标识",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    RuleSchema(
        key="title",
        name="视频标题",
        type="string",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频的标题内容",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    RuleSchema(
        key="author_account_id",
        name="作者账号ID",
        type="string",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频作者的账号ID",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    RuleSchema(
        key="author_name",
        name="作者名称",
        type="string",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频作者的名称",
        show_in_prompt=ShowInPrompt.AlwaysShow,
    ),
    RuleSchema(
        key="daren_id",
        name="达人ID",
        type="string",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="达人的唯一标识",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    # 核心指标字段
    RuleSchema(
        key="like_cnt",
        name="点赞数",
        type="float",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频的点赞数量",
        show_in_prompt=ShowInPrompt.AlwaysShow,
    ),
    RuleSchema(
        key="comment_cnt",
        name="评论数",
        type="float",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频的评论数量",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    RuleSchema(
        key="share_cnt",
        name="分享数",
        type="float",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频的分享数量",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    RuleSchema(
        key="favourite_cnt",
        name="收藏数",
        type="float",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频的收藏数量",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    RuleSchema(
        key="view_cnt",
        name="播放数",
        type="float",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频的播放数量",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    RuleSchema(
        key="sales_amount",
        name="销售额",
        type="float",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频带货的销售额",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    RuleSchema(
        key="sales_volume",
        name="销售量",
        type="float",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频带货的销售量",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    RuleSchema(
        key="duration",
        name="视频时长",
        type="float",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频时长，单位为秒",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    RuleSchema(
        key="author_fans_cnt",
        name="作者粉丝数",
        type="float",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频作者的粉丝数量，整数类型，与 author_fans_range 参数互斥。提示：KOC 指 <100,000粉丝，KOL 指 >100,000万粉丝",
        show_in_prompt=ShowInPrompt.AlwaysShow,
    ),
    RuleSchema(
        key="interaction_score",
        name="互动指数",
        type="int",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="综合互动指数，基于点赞、评论、分享数计算",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    # 分类维度字段
    RuleSchema(
        key="author_fans_range",
        name="作者粉丝等级",
        type="string",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description='根据粉丝数划分的作者等级，与 author_fans_cnt 参数互斥，只能从以下值中选择：["<1w", "1w-10w", "10w-100w", "100w-500w", ">500w"]。分别对应用户说的“尾部达人”、“小达人”、“腰部达人”、“肩部达人”、“头部达人”',
        show_in_prompt=ShowInPrompt.AlwaysShow,
    ),
    RuleSchema(
        key="posting_time_slot",
        name="发布时间段",
        type="string",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频发布的时间段",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    RuleSchema(
        key="promotion_type",
        name="推广类型",
        type="string",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频的推广类型",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    RuleSchema(
        key="is_bring_goods",
        name="是否带货",
        type="int",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频是否包含带货内容，1表示带货，0表示不带货",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    # 时间字段
    RuleSchema(
        key="create_date",
        name="创建日期",
        type="date",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频的创建日期，格式为'YYYY-MM-DD'",
        show_in_prompt=ShowInPrompt.AlwaysShow,
    ),
    RuleSchema(
        key="create_time",
        name="创建时间",
        type="datetime",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频的创建时间",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    RuleSchema(
        key="create_month",
        name="创建月份",
        type="string",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频创建的月份，格式为yyyy-MM",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    RuleSchema(
        key="create_year",
        name="创建年份",
        type="string",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频创建的年份",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    # 数据来源字段
    RuleSchema(
        key="data_source",
        name="数据来源",
        type="string",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频数据的来源",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    RuleSchema(
        key="data_platform",
        name="数据平台",
        type="string",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频所属的数据平台",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    # 数组字段
    RuleSchema(
        key="rank_type",
        name="排行榜类型",
        type="array",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description='视频所在的排行榜类型，只能从以下值中选择：["低粉爆款","高点赞率","高完播率"]',
        show_in_prompt=ShowInPrompt.AlwaysShow,
    ),
    # 数组字段
    RuleSchema(
        key="gender",
        name="观众性别偏好",
        type="array",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description='视频的观众的性别偏好，只能从["male", "female"]中选择',
        show_in_prompt=ShowInPrompt.AlwaysShow,
    ),
    RuleSchema(
        key="bring_goods",
        name="带货商品",
        type="array",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频中的带货商品列表",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    RuleSchema(
        key="hot_comment_words",
        name="热门评论词",
        type="array",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频热门评论中的关键词",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    RuleSchema(
        key="category1",
        name="主要分类",
        type="array",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频的主要分类标签",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
    RuleSchema(
        key="category2",
        name="子分类",
        type="array",
        condition=CategoryInfo(),
        physical_field=None,
        ai_description="视频的子分类标签",
        show_in_prompt=ShowInPrompt.AlwaysHide,
    ),
]


# =============================================================================
# 分类专属规则定义
# =============================================================================

# 时尚-穿搭类视频属性
video_attribute_rules.extend(
    [
        RuleSchema(
            key="fashion_dress_content_style",
            name="内容形式",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_content_style', 'mapped_value', 'Array(String)'))",
            ai_description="视频内容的表现形式，使用中文",
            boost=10,
        ),
        RuleSchema(
            key="fashion_dress_content_type",
            name="视频类型",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_content_type', 'mapped_value', 'Array(String)'))",
            ai_description="视频的具体类型，使用中文",
            boost=5,
        ),
        RuleSchema(
            key="fashion_dress_show_way",
            name="表现手法",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_show_way', 'mapped_value', 'Array(String)'))",
            ai_description="视频的拍摄和展示手法，使用中文",
            boost=10,
        ),
        RuleSchema(
            key="fashion_dress_opening_hook",
            name="开头钩子",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_opening_hook', 'mapped_value', 'Array(String)'))",
            ai_description="视频开头的互动钩子，使用中文",
            boost=10,
        ),
        RuleSchema(
            key="fashion_dress_video_scene",
            name="视频场景",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_video_scene', 'mapped_value', 'Array(String)'))",
            ai_description="视频展示的场景类型，使用中文",
            boost=5,
        ),
        RuleSchema(
            key="fashion_dress_topic_tag",
            name="话题标签",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_topic_tag', 'mapped_value', 'Array(String)'))",
            ai_description="视频相关的话题标签，使用中文",
            boost=10,
        ),
        RuleSchema(
            key="fashion_dress_video_highlight",
            name="视频亮点",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_video_highlight', 'mapped_value', 'Array(String)'))",
            ai_description="视频中的亮点信息，使用中文",
            boost=5,
        ),
        RuleSchema(
            key="fashion_dress_item_type",
            name="服饰品类",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_item_type', 'mapped_value', 'Array(String)'))",
            ai_description="视频中展示的服饰类型，使用中文",
            boost=30,
            must_filter=True,
        ),
        RuleSchema(
            key="fashion_dress_style",
            name="穿搭风格",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_style', 'mapped_value', 'Array(String)'))",
            ai_description="视频展示的穿搭风格，使用中文",
            boost=5,
        ),
        RuleSchema(
            key="fashion_dress_color",
            name="颜色识别",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_color', 'mapped_value', 'Array(String)'))",
            ai_description="视频中服饰的主要颜色，使用中文",
            boost=1,
        ),
        RuleSchema(
            key="fashion_dress_pattern",
            name="图案识别",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_pattern', 'mapped_value', 'Array(String)'))",
            ai_description="服饰的图案类型，使用中文",
            boost=1,
        ),
        RuleSchema(
            key="fashion_dress_fabric",
            name="面料材质",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_fabric', 'mapped_value', 'Array(String)'))",
            ai_description="服饰的面料材质，使用中文",
            boost=5,
        ),
        RuleSchema(
            key="fashion_dress_craftsmanship",
            name="工艺细节",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_craftsmanship', 'mapped_value', 'Array(String)'))",
            ai_description="服饰的工艺特点，使用中文",
            boost=5,
        ),
        RuleSchema(
            key="fashion_dress_season",
            name="季节关键词",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_season', 'mapped_value', 'Array(String)'))",
            ai_description="适用的季节或时间，使用中文",
            boost=10,
        ),
        RuleSchema(
            key="fashion_dress_selling_points",
            name="核心卖点",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_selling_points', 'mapped_value', 'Array(String)'))",
            ai_description="服饰的主要卖点，使用中文",
            boost=5,
        ),
        RuleSchema(
            key="fashion_dress_target_audience",
            name="目标受众",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_target_audience', 'mapped_value', 'Array(String)'))",
            ai_description="视频的目标观众群体，使用中文",
            boost=10,
        ),
        RuleSchema(
            key="fashion_dress_body_shape",
            name="身材适配",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_body_shape', 'mapped_value', 'Array(String)'))",
            ai_description="适合的身材类型，使用中文",
            boost=10,
        ),
        RuleSchema(
            key="fashion_dress_usage_scene",
            name="适用场景",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_usage_scene', 'mapped_value', 'Array(String)'))",
            ai_description="服饰的使用场合，使用中文",
            boost=5,
        ),
        RuleSchema(
            key="fashion_dress_influencer_age",
            name="达人年龄段",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_influencer_age', 'mapped_value', 'Array(String)'))",
            ai_description="视频博主的年龄描述，使用中文",
            boost=10,
        ),
        RuleSchema(
            key="fashion_dress_influencer_body",
            name="达人体型",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_influencer_body', 'mapped_value', 'Array(String)'))",
            ai_description="视频博主的体型描述，使用中文",
            boost=10,
        ),
        RuleSchema(
            key="fashion_dress_influencer_vibe",
            name="达人气质",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_influencer_vibe', 'mapped_value', 'Array(String)'))",
            ai_description="视频博主的气质描述，使用中文",
            boost=10,
        ),
        RuleSchema(
            key="fashion_dress_price",
            name="价位感知",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_price', 'mapped_value', 'Array(String)'))",
            ai_description="视频中服饰的价位感知，使用中文",
            boost=5,
        ),
        RuleSchema(
            key="fashion_dress_creation_difficulty",
            name="创作难度",
            type="string",
            condition=CategoryInfo(category1="时尚", category2="穿搭"),
            physical_field="arrayJoin(JSONExtract(main.attributes, 'fashion_dress_creation_difficulty', 'mapped_value', 'Array(String)'))",
            ai_description="视频创作难易程度，使用中文",
            boost=5,
        ),
    ]
)
