"""
请求处理工具函数
提供与HTTP请求相关的工具函数
"""

from fastapi import Request


def get_remote_client_host(request: Request) -> str:
    """
    获取客户端真实IP地址

    优先从代理头中获取客户端IP，如果没有则使用request.client.host

    Args:
        request: FastAPI Request对象

    Returns:
        str: 客户端IP地址
    """
    for key in [
        "X-Forwarded-For",
        "X-Real-IP",
        "Proxy-Client-IP",
        "WL-Proxy-Client-IP",
        "HTTP_CLIENT_IP",
    ]:
        if request.headers.get(key):
            return request.headers.get(key)

    return request.client.host  # type: ignore
