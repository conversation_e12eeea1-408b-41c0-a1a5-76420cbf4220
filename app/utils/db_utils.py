"""
数据库查询和数据处理工具函数

为了避免循环导入，将一些通用的数据库工具函数从 content_modules.utils 中分离出来
"""

import json
from dataclasses import asdict
from typing import Any

from dataclass_wizard import fromdict
from pydantic import BaseModel

from app.logger import logger
from app.service.search_agent.data_def import DbQueryResult
from app.service.search_agent.query_db import (
    DbQueryState,
    SqlListResponse,
    SqlResponse,
    dsl_to_sql,
    db_query_node,
)
from app.utils.dsl_info import SqlQueryInfo


# 表格占位符常量
OTHER_CELL_PLACEHOLDER = "其他"
TOTAL_CELL_PLACEHOLDER = "总计"


def _to_json(d: Any) -> str:
    """
    将数据转换为 JSON 字符串
    """
    if isinstance(d, BaseModel):
        return json.dumps(d.model_dump(exclude_none=True), ensure_ascii=False)
    elif hasattr(d, "__dataclass_fields__"):
        d = asdict(d)
        d = {k: v for k, v in d.items() if v is not None}
        return json.dumps(d, ensure_ascii=False)
    elif isinstance(d, list):
        return json.dumps([_to_json(item) for item in d], ensure_ascii=False)
    elif isinstance(d, dict):
        return json.dumps(d, ensure_ascii=False)
    else:
        return str(d)


def sql_query_to_data(
    dsl: str | SqlQueryInfo,
    title: str = "",
    format_float: bool = True,
) -> DbQueryResult:
    """
    将 DSL 转换为数据
    """
    if isinstance(dsl, str):
        dsl = fromdict(SqlQueryInfo, json.loads(dsl))

    # 1. dsl_to_sql
    sql = dsl_to_sql(dsl)
    dsl_str = _to_json(dsl)
    data = sql_to_data(sql, dsl_str, title, format_float=format_float)

    return data


def sql_to_data(
    sql_string: str,
    dsl_string: str = "",
    title: str = "",
    engine="dw_ch_engine",
    format_float: bool = True,
) -> DbQueryResult:
    """
    将 SQL 转换为数据
    """
    state = DbQueryState(
        messages=[],
        db_query_result_list=[],
        remaining_steps=0,
        sql_list_response=SqlListResponse(
            sql_list=[
                SqlResponse(
                    sql_string=sql_string,
                    dsl_string=dsl_string,
                    query_description="",
                    result_title=title,
                )
            ]
        ),
        engine=engine,
        format_float=format_float,
    )

    query_result = db_query_node(state)
    result_list = query_result.get("db_query_result_list", [])
    for result_data in result_list:
        result_data.pop("query")  # type: ignore

    if not result_list or len(result_list) == 0:
        logger.warning(f"{title} 的 SQL 查询无数据")
        data = {"title": title, "error_message": "Error：查询结果为空"}
    else:
        data = result_list[0]

    # filter empty row
    _filter_empty_row(data)

    return data  # pyright: ignore [reportReturnType]


def _filter_empty_row(result: Any):
    """过滤空行"""
    if not result:
        return

    if isinstance(result, list):
        for item in result:
            _filter_empty_row(item)
        return

    if not isinstance(result, dict) or "result_row_list" not in result:
        return

    processed_rows = []
    for row in result["result_row_list"]:
        if not row[0] or 0 == row[0] or "0" == row[0]:
            continue
        processed_rows.append(row)
    result["result_row_list"] = processed_rows
