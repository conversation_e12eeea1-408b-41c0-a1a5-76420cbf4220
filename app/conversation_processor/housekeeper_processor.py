from abc import abstractmethod
import asyncio
import threading
import time
import traceback

from langfuse.decorators import observe, langfuse_context
from langgraph.types import Command
from langchain_core.runnables.config import RunnableConfig
from opentelemetry import trace
import opentelemetry.context
from typing_extensions import Optional

from app.db.common_type import Cha<PERSON><PERSON><PERSON><PERSON>, ContentType, ChatCommand
from app.db.repository import chat_repository
from app.logger import logger
from app.conversation_type import (
    HOUSE_KEEPER_INIT_CONTENT,
    HOUSE_KEEPER_INIT_OPTIONS,
    SnsPlatform,
)
from .base_processor import BaseProcessor, ToolCall
from .tool_functions import ChoiceQuestion
from ..client.prompts import TEMPLATES
from ..db.models import ChatEntity
from ..service.search_agent.data_def import (
    ProgressInfo,
    HousekeeperProgressData,
    ProgressStepInfo,
    HK_THREAD_PROGRESS_MAP,
    AssistantName,
)
from ..service.search_agent.housekeeper_agent import (
    ECOMMERCE_HK_INSTANCE,
    HousekeeperAgentBase,
)
from ..service.search_agent.utils import (
    create_progress_data,
)
from ..utils import langfuse_utils
from app.config import G<PERSON><PERSON><PERSON>L_CONF
from app.env import SIHEGPT_STAGE
from app.service.activity.collector import collect_event
from app.db.repository.chat_repository import get_latest_messages
from app.utils.robot_utils import build_es_link, notify_robot
from urllib.parse import quote


class HousekeeperThread(threading.Thread):
    def __init__(self, graph, graph_input, graph_config, thread_id, trace_id):

        super().__init__(daemon=True, name=f"housekeeper-{str(thread_id)[:4]}")
        # 子线程继承当前线程的 trace context （
        self.opentelemetry_context = opentelemetry.context.get_current()
        self.graph = graph
        self.result = None
        self.graph_input = graph_input
        self.graph_config = graph_config
        self.is_running = True
        self.thread_id = thread_id
        self.is_error = False
        self.should_stop = False
        self.trace_id = trace_id

    @observe
    def _call_housekeeper_graph(self):
        for event in self.graph.stream(self.graph_input, config=self.graph_config):
            if self.should_stop:
                return
            # 选择题实现原理：
            # 1. langgraph 的 __interrupt__ 节点会中断当前的流式输出，并返回一个选择题对象
            # 2. 写入到 progress_data 中
            # 3. 在 _update_chat_by_progress_data 中，会根据 progress_data 中的 question 字段，生成一个选择题的 Chat 对象返回给前端
            # 当用户选择选项后，会调用 Command(resume=decision) ，继续之前的流式输出
            if "__interrupt__" in event:
                print("------- __interrupt__ -------")
                print(event["__interrupt__"])
                # 要求 interrupt 返回一个 ChoiceQuestion 对象
                question = event["__interrupt__"][0].value
                assert isinstance(question, ChoiceQuestion)
                progress_data: HousekeeperProgressData = HK_THREAD_PROGRESS_MAP[self.thread_id]  # type: ignore
                progress_data.question = question
                return
            if "messages" in event:
                event["messages"][-1].pretty_print()
                continue
            for k in event.keys():
                value = event[k]
                if value is None or "messages" not in value:
                    continue
                last_message = value["messages"][-1]
                last_message.pretty_print()

    def run(self):
        try:
            token = opentelemetry.context.attach(self.opentelemetry_context)
            trace_id2 = format(
                trace.get_current_span().get_span_context().trace_id, "032x"
            )
            logger.info(
                f"Running housekeeper thread, trace_id: {self.trace_id}, trace_id2: {trace_id2}"
            )
            self._call_housekeeper_graph(
                **{"langfuse_observation_id": self.trace_id},
            )
            progress_data: HousekeeperProgressData = HK_THREAD_PROGRESS_MAP[self.thread_id]  # type: ignore
            progress_data.set_end_time(time.time())
        except Exception as e:
            self.is_error = True
            logger.error(e, exc_info=True)
            if str(e) == "会话已被中止":
                return
            self.notify_housekeeper_failure(
                error_message=str(e),
                error_traceback=traceback.format_exc(),
                trace_id=self.trace_id,
            )
        finally:
            self.is_running = False
            opentelemetry.context.detach(token)

    @staticmethod
    def notify_housekeeper_failure(
        error_message: str,
        error_traceback: str,
        trace_id: str,
    ):
        if SIHEGPT_STAGE == "DEV":
            logger.warning(f"DEV 环境跳过飞书通知: housekeeper 失败 - {error_message}")
            return

        es_link = build_es_link(trace_id)
        trace_url = langfuse_context.get_current_trace_url()

        try:
            content = {
                "title": "⚠️ Housekeeper Agent 运行失败",
                "content": [
                    [
                        {
                            "tag": "text",
                            "text": "Housekeeper Agent 运行失败，请立即排查并修复！",
                        },
                    ],
                    [
                        {
                            "tag": "text",
                            "text": f"Trace ID: {trace_id} ",
                        },
                        {
                            "tag": "a",
                            "text": "[Trace]",
                            "href": trace_url,
                        },
                        {
                            "tag": "text",
                            "text": " | ",
                        },
                        {
                            "tag": "a",
                            "text": "[ES日志]",
                            "href": es_link,
                        },
                    ],
                    [
                        {"tag": "text", "text": f"错误信息: {error_message}"},
                    ],
                    [
                        {
                            "tag": "text",
                            "text": "错误堆栈: "
                            + (
                                error_traceback[:1000] + "..."
                                if len(error_traceback) > 1000
                                else error_traceback
                            ),
                        },
                    ],
                    [
                        {
                            "tag": "text",
                            "text": f"环境: {SIHEGPT_STAGE}",
                        },
                    ],
                ],
            }
            notify_robot(GLOBAL_CONF.WEBHOOK_CHAT_VOTING, content)
            logger.info("已发送 housekeeper 失败的飞书通知")
        except Exception as e:
            logger.error(f"发送 housekeeper 失败的飞书通知时出错: {e}")


class HousekeeperProcessor(BaseProcessor):
    """
    这个类算是 housekeeper 风格的基类，作为复杂规划、文档复的基础。目前子类有：
    - EcommerceHousekeeperProcessor: 电商助手 （原HousekeeperProcessor）
    - ContentAssistantV4Processor: 内容助手v4
    """

    def show_progress_default(self) -> bool:
        return False

    @abstractmethod
    def get_hk_agent(self) -> HousekeeperAgentBase:
        pass

    async def do_reply(self):
        update_chat = chat_repository.updater(self.user_id, self.conversation_id)
        try:
            await self._do_reply(update_chat)
        except Exception as e:
            logger.warning(e, exc_info=True)
            update_chat(
                str(self.response_chat_id),
                content="出错了，请稍候重试。。。",
                progress=1,
                status=ChatStatus.error,
            )  # type: ignore

    @observe(name="housekeeper_agent")
    async def _do_reply(self, update_chat):
        messages = self.get_message_list()
        loop_counter = 0
        start_time = time.time()
        progress_data = create_progress_data(start_time)
        assert self.chat

        # 收集助手查询事件（只对新的用户查询收集，不对决策或重试收集）
        if (
            self.content_type not in [ContentType.decision]
            and self.chat_command != ChatCommand.retry
        ):
            try:
                # 获取会话的完整消息统计
                def get_conversation_message_stats():
                    try:
                        # 获取会话中的所有消息（不限制数量）
                        all_messages = get_latest_messages(
                            sender_id=self.user_id,
                            conversation_id=self.conversation_id,
                            start_time="2000-01-01 00:00:00",  # 从很早的时间开始获取所有消息
                            limit=10000,  # 设置一个足够大的限制
                            order_desc=False,  # 按时间升序
                            include_updated=True,
                            filter_cleared=True,  # 过滤掉清除上下文之前的消息
                        )

                        # 统计用户消息和总消息数量
                        user_message_count = len(
                            [
                                msg
                                for msg in all_messages
                                if msg.sender_type.value == "user"
                            ]
                        )
                        total_message_count = len(all_messages)

                        return user_message_count, total_message_count
                    except Exception as e:
                        logger.warning(f"Failed to get conversation message stats: {e}")
                        # 降级到使用当前的messages（最近10条）
                        user_count = len(
                            [msg for msg in messages if msg.sender_type.value == "user"]
                        )
                        total_count = len(messages)
                        return user_count, total_count

                user_message_count, total_message_count = (
                    get_conversation_message_stats()
                )

                # 获取助手类型
                assistant_name = self.get_assistant_name().value

                collect_event(
                    event_name="housekeeper:query",
                    user_id=str(self.user_id),
                    source="backend",
                    event_args={
                        "assistant_type": assistant_name,
                        "message_content": self.content,
                        "conversation_id": self.conversation_id,
                        "user_message_count": user_message_count,  # 用户消息总数
                        "total_message_count": total_message_count,  # 总消息数量
                        "history_message_count": user_message_count,  # 保持向后兼容
                    },
                )
            except Exception as e:
                # 事件收集失败不应该影响主流程
                logger.warning(f"Failed to collect housekeeper:query event: {e}")

        thread_id = self.response_chat_id
        if self.content_type == ContentType.decision or (
            # 重新回答时，在 base processor 中会将 self.chat 改为 prev chat
            self.chat_command == ChatCommand.retry
            and self.chat.content_type == ContentType.choice  # type: ignore
        ):
            chats = chat_repository.get_latest_messages(
                self.user_id,
                self.conversation_id,
                chat_repository.INIT_TIME,
                limit=20,
                order_desc=True,
                include_hidden=True,
            )  # type: ignore
            # graph state 的 id 是第一条选择题的 chat id
            first_question_chat_id = None
            for chat in chats[1:]:  # 最新消息为当前消息，需要过滤
                if chat.content_type == ContentType.choice:
                    first_question_chat_id = chat.chat_id
                    continue
                if chat.content_type != ContentType.choice:
                    break
            logger.info(f"复用 graph state, thread_id: {first_question_chat_id}")
            thread_id = first_question_chat_id

        # 5.16 临时修改，由于content_assistant_v4获得的消息没有sns_platforms字段了
        # 获取sns_platforms，如果为空或包含None，则使用默认值DOUYIN
        sns_platforms = self.content_json.get("sns_platforms", []) or [
            SnsPlatform.DOUYIN
        ]
        # 确保sns_platforms中没有None值
        if not sns_platforms or sns_platforms[0] is None:
            sns_platform = SnsPlatform.DOUYIN
        else:
            sns_platform = SnsPlatform(sns_platforms[0])

        # graph_input
        if self.chat_command == ChatCommand.retry:
            graph_input = None
        elif self.content_type == ContentType.decision:
            graph_input = Command(resume=self.content_json["decision"])
        else:  # init graph
            graph_input = HousekeeperAgentBase.create_housekeeper_input(
                messages,
                self.user_id,
                self.conversation_id,
                str(thread_id),
                sns_platform,
                self.get_assistant_name(),
                self.chat.content_json,
            )

        # graph_config
        housekeeper_graph = self.get_hk_agent().housekeeper_graph
        graph_config = RunnableConfig(
            configurable={"thread_id": thread_id},
        )
        if self.chat_command == ChatCommand.retry:
            # 暂定重新回答不会重新出选择题
            # 将 state 退回到 user_input_tool_node 的下一步
            # 如果没有 user_input_tool_node 则退回到 __start__（没出选择题直接回答了）
            # 备忘：如果需求改为需要重新出选择题，则需将 state 退回 __start__，且需要处理之前出过的选择题（删除或隐藏）
            state_history = housekeeper_graph.get_state_history(graph_config)
            all_states = []
            for s in state_history:
                all_states.append(s)
                if len(s.next) > 0 and s.next[0] == "user_input_tool_node":
                    graph_config = all_states[-1].config
                    break
                # 如果有 user_input_tool_node，__start__ 也一定在后面，这里不用两遍循环
                if len(s.next) > 0 and s.next[0] == "__start__":
                    graph_config = s.config
                    break

        current_span = trace.get_current_span()
        trace_id = format(current_span.get_span_context().trace_id, "032x")

        HK_THREAD_PROGRESS_MAP[thread_id] = progress_data
        logger.info(
            f"Start housekeeper thread, thread_id: {thread_id}, trace_id: {trace_id}"
        )
        housekeeper_thread = HousekeeperThread(
            housekeeper_graph,
            graph_input,
            graph_config,
            thread_id,
            trace_id,
        )

        def _update_chat_by_progress_data(
            is_stream_done,
        ) -> bool:  # return need_response_options
            """更新 chat 状态，并返回是否需要生成 options"""
            nonlocal loop_counter
            progress_data.set_end_time(time.time())
            if progress_data.question is not None:
                chat_repository.update_chat(
                    sender_id=self.user_id,
                    conversation_id=self.conversation_id,
                    chat_id=self.response_chat_id,  # type: ignore
                    progress=1,
                    content_type=ContentType.choice,
                    content_json={
                        "choice": progress_data.question.to_json(),
                        "following_questions": [],
                    },
                    status=ChatStatus.done,
                )
                return False  # 选择题，提前退出
            tmp_chat = HousekeeperProcessor._progress_data_to_chat(
                progress_data, is_stream_done
            )
            if tmp_chat is None:
                return False
            update_chat(
                self.response_chat_id,
                content=tmp_chat.content,
                progress=HousekeeperProcessor.calc_progress_update(
                    loop_counter, progress_data
                ),
                content_json=tmp_chat.content_json,
                docs=tmp_chat.docs,
                videos=tmp_chat.videos,
                status=ChatStatus.ongoing,
                options=tmp_chat.options if is_stream_done else None,
            )
            return True

        try:
            housekeeper_thread.start()
            while housekeeper_thread.is_running:
                _update_chat_by_progress_data(False)
                await asyncio.sleep(1)
            # final update
            need_response_options = _update_chat_by_progress_data(True)
        except Exception as e:
            housekeeper_thread.should_stop = True
            raise e
        finally:
            housekeeper_thread.join()

        update_chat(
            self.response_chat_id,
            progress=1,
            status=ChatStatus.done,
        )
        if need_response_options:
            await self.generate_response_options(messages)

    def get_response_processing_content(self) -> tuple[str, ContentType]:
        return "正在理解问题中 <loading-img-1></loading-img-1>", ContentType.normal

    @staticmethod
    def calc_progress_update(loop_counter, progress_data: HousekeeperProgressData):
        done = 0
        target = 0
        # 按时间流动的进度（目标是300秒/次循环）
        time_progress = min(loop_counter, 300) / 300
        # 按搜索结果的进度
        doc_progress = 0
        step_done = 0
        step_total = 0
        if len(progress_data.progressInfo.step_list) == 0:
            return 1.0
        for step in progress_data.progressInfo.step_list:
            step_total += 1
            if step.step_is_done:
                step_done += 1
            elif step.step_detail_list:
                step_done += 0.5
        if step_total > 0:
            doc_progress = step_done / step_total

        # 按输出文字数量进度计算(10000字)
        final_answer_progress = progress_data.number_of_chars_generated / 10000
        final_answer_progress = min(final_answer_progress, 1)

        # 将上述几个进度加权平均（根据trace的timeline经验而得）
        progress = (
            time_progress * 0.1 + doc_progress * 0.2 + final_answer_progress * 0.7
        )
        progress = min(progress, 0.99)
        return progress

    @staticmethod
    def _get_progress_info(
        progress_data: HousekeeperProgressData,
    ) -> Optional[ProgressInfo]:
        """
        将思考进度数据转换为 ProgressInfo 对象，适用于展示思考过程、显示最终答案
        根据需求文档（新）：https://cybercore.feishu.cn/wiki/FaRlwcBisi3iMmk5738cukXOnod#share-GjPgdqbsqofk6MxInnIcJypRn7d
        我们需要将思考过程以 markdown 形式展示给前端，字段为step_list[0].step_title
        """
        if len(progress_data.progressInfo.step_list) == 0:
            return None

        # 创建一个新的 ProgressInfo 对象
        new_progress_info = ProgressInfo(
            chat_header=progress_data.progressInfo.chat_header,
            step_status="💡 我的想法",
            duration_in_seconds=progress_data.progressInfo.duration_in_seconds,
            step_list=[],
        )

        # 拼接所有步骤的信息到一个 markdown 字符串
        markdown_content = ""

        for step in progress_data.progressInfo.step_list:
            if step.step_title:
                # 将每个步骤的标题作为 markdown H1
                markdown_content += (
                    "# " + step.step_title.replace("\n", " ").strip() + "\n\n"
                )

            # 添加步骤的详情（如果有）
            if step.step_detail_list and len(step.step_detail_list) > 0:
                for detail in step.step_detail_list:
                    markdown_content += f"{detail}\n\n"

        # 创建一个新的 ProgressStepInfo，包含拼接好的 markdown 内容
        new_step = ProgressStepInfo(
            step_title=markdown_content.strip(),
            step_tags=[],
            step_detail="",
            step_detail_list=[],
            step_is_done=True,
        )

        # 将新的步骤添加到步骤列表
        new_progress_info.step_list.append(new_step)

        return new_progress_info

    @staticmethod
    def _progress_data_to_chat(
        progress_data: HousekeeperProgressData, is_stream_done: bool
    ) -> Optional[ChatEntity]:
        """将思考进度数据转换为 chat 对象，适用于流式显示气泡、展示思考过程、显示最终答案"""
        chat = ChatEntity()
        # 这里是流式显示气泡的入口
        chat.content = progress_data.final_answer_str  # type: ignore
        if is_stream_done:
            ref_links = progress_data.ref_links
            if len(ref_links) > 0:
                chat.content += "\n\n<hr>\n\n相关资料如下，请点击查看：\n\n"  # type: ignore
            for ref_link in ref_links:
                chat.content += f'* [{ref_link["title"]}]({ref_link["url"]})\n\n'  # type: ignore
        chat.docs = progress_data.final_doc_list  # type: ignore
        chat.videos = progress_data.final_video_list  # type: ignore
        chat.options = progress_data.options  # type: ignore

        progress_info = HousekeeperProcessor._get_progress_info(progress_data)
        chat.content_json = {}
        if progress_info is not None:
            chat.content_json = {"progress_info": progress_info.model_dump()}
        if len(chat.content) == 0 and progress_info is None:  # type: ignore
            return None  # 还在处理问题，不更新消息，使用原占位符
        return chat

    def get_assistant_name(self) -> AssistantName:
        return AssistantName.housekeeper


class EcommerceHousekeeperProcessor(HousekeeperProcessor):
    """
    电商助手，继承自 HousekeeperProcessor，行为与后者完全相同
    """

    @staticmethod
    def get_welcome_message_and_options() -> tuple[str, list[str]]:
        """
        获取欢迎消息和选项，供新建会话时使用

        Returns:
            tuple[str, list[str]]: (欢迎消息, 选项列表)
        """
        return HOUSE_KEEPER_INIT_CONTENT, HOUSE_KEEPER_INIT_OPTIONS

    def get_hk_agent(self) -> HousekeeperAgentBase:
        return ECOMMERCE_HK_INSTANCE
