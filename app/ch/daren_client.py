import json
from typing import Optional

from app.ch.model import ModelAggDarenBasicInfo, DarenDisassembleReport
from app.ch.orm import ChClient
from infi.clickhouse_orm import Database, F, QuerySet, Q


class DarenClient:
    def __init__(self):
        self.ch_client = ChClient()

    def query_only_daren_name_by_page(
        self, offset: int, limit: int
    ) -> list[ModelAggDarenBasicInfo]:
        return [
            item
            for item in ModelAggDarenBasicInfo.objects_in(self.ch_client.dw_db)
            .only("daren_name")
            .order_by("daren_id")[offset : offset + limit]
        ]

    def get_daren_by_name(self, name: str) -> Optional[ModelAggDarenBasicInfo]:
        queryset = ModelAggDarenBasicInfo.objects_in(self.ch_client.dw_db).filter(
            ModelAggDarenBasicInfo.daren_name == name
        )
        if queryset.count() == 0:
            return None
        return queryset[0]  # type: ignore

    def get_daren_by_id(self, id: str) -> Optional[ModelAggDarenBasicInfo]:
        queryset = ModelAggDarenBasicInfo.objects_in(self.ch_client.dw_db).filter(
            ModelAggDarenBasicInfo.daren_id == id
        )
        if queryset.count() == 0:
            return None
        return queryset[0]  # type: ignore

    def get_daren_by_source_and_name(
        self, source: str, name: str
    ) -> Optional[ModelAggDarenBasicInfo]:
        queryset = ModelAggDarenBasicInfo.objects_in(self.ch_client.dw_db).filter(
            ModelAggDarenBasicInfo.source == source,
            ModelAggDarenBasicInfo.daren_name == name,
        )
        if queryset.count() != 0:
            return queryset[0]  # type: ignore

        queryset = ModelAggDarenBasicInfo.objects_in(self.ch_client.dw_db).filter(
            ModelAggDarenBasicInfo.source == source,
            F.like(ModelAggDarenBasicInfo.daren_name, f"%{name}%"),
        )
        if queryset.count() != 0:
            return queryset[0]  # type: ignore
        return None

    def get_daren_disassemble_report(
        self, source: str, daren_id: str
    ) -> Optional[DarenDisassembleReport]:
        if source == "tiktok":
            source = "tiktok_us"
        queryset = DarenDisassembleReport.objects_in(self.ch_client.db).filter(
            DarenDisassembleReport.source == source,
            DarenDisassembleReport.daren_id == daren_id,
        )
        if queryset.count() == 0:
            return None
        return queryset[0]  # type: ignore

    def update_daren_disassemble_report(self, new_report: DarenDisassembleReport):
        queryset = DarenDisassembleReport.objects_in(self.ch_client.db).filter(
            DarenDisassembleReport.source == new_report.source,
            DarenDisassembleReport.daren_id == new_report.daren_id,
        )

        queryset.update(
            report_json=json.dumps(new_report.report_json, ensure_ascii=False),
            marked_report_json=(
                json.dumps(new_report.marked_report_json, ensure_ascii=False)
                if new_report.marked_report_json
                else None
            ),
        )
