# TODO(dongzhou.xie): 修复本文件的类型错误
import time
import asyncio
import json
from datetime import datetime, timedelta
import pandas as pd

from typing import List, Optional

from infi.clickhouse_orm import Database, F, QuerySet, Q
from infi.clickhouse_orm.utils import comma_join, arg_to_sql
from typing_extensions import TypedDict, Optional, Any
import requests
import urllib3
from tenacity import retry, stop_after_attempt, retry_if_exception_type

from app.category_config import (
    CategoryInfo,
    create_category_info,
    CategoryMerger,
    CategoryWhitelistConfig,
)
from app.ch.model import (
    BaikeEntry,
    User,
    Video,
    Comment,
    CreatorVideo,
    CreatorUser,
    VideoMark,
    VideoToMark,
    Daren,
    DarenVideoDisassemble,
    DarenVideoDisassembleResult,
    UserActionData,
    VIDEO_MARK_AUDIT_RESULT_PASS,
    VIDEO_MARK_AUDIT_RESULT_FAIL,
    VIDEO_SOURCE_ARTIFICIAL,
    <PERSON><PERSON>otRank,
    DouhotAccountInfo,
    Do<PERSON>otVideoInfo,
    DouhotSentenceInfo,
    DouhotTopicInfo,
    TiktokVideoInfo,
    JSONExtractString,
    FactDouhotTopic,
    DimBrand,
    DimProduct,
    DimDaren,
    ETLStructuredDouyinVideo,
    DimShop,
    ETLStructuredDouyinDaren,
    ModelAggDarenBasicInfo,
    DimCategory,
    DictProductAttributes,
    XinhongAccountDetail,
    XiaohongshuNoteInfo,
    XinhongAccountHistoryNotes,
    AggProductBaseInfo,
    DictVideoAttributes,
    DictVideoAttributesV2,
    ModelAggProductBaseInfo,
    ModelProcessProductAIRecognitionResult,
    ModelFactProductComment,
    EmbeddingText,
    EmbeddingTextWithCosineDistance,
    EmbeddingTextType,
    EmbeddingTextItem,
    ModelFactProductCommentAnalysis,
    EtlNoxProductVideoList,
    JSONExtractInt,
    EtlNoxDarenVideoList,
    DwsAttributesMapping,
    ModelAggVideoBaseInfoV2,
)
from app.db.common_type import UserActionType, UserActionTarget, CrawlFromType
from app.config import GLOBAL_CONF
from app.logger import logger


CRAWL_DAYS_BEFORE = 7


def update_on_distributed_table(querySet: QuerySet, **kwargs):
    if GLOBAL_CONF.CH_HOST == "***********":
        querySet.update(**kwargs)
        return querySet

    assert kwargs, "No fields specified for update"
    querySet._verify_mutation_allowed()
    fields = comma_join(
        "`%s` = %s" % (name, arg_to_sql(expr)) for name, expr in kwargs.items()
    )
    conditions = (querySet._where_q & querySet._prewhere_q).to_sql(querySet._model_cls)
    sql = "ALTER TABLE $db.`%s_local` ON CLUSTER '{cluster}' UPDATE %s WHERE %s" % (
        querySet._model_cls.table_name(),
        fields,
        conditions,
    )
    querySet._database.raw(sql)
    return querySet


def delete_on_distributed_table(querySet: QuerySet):
    if GLOBAL_CONF.CH_HOST == "***********":
        querySet.delete()
        return querySet

    querySet._verify_mutation_allowed()
    conditions = (querySet._where_q & querySet._prewhere_q).to_sql(querySet._model_cls)
    sql = "ALTER TABLE $db.`%s_local` ON CLUSTER '{cluster}' DELETE WHERE %s" % (
        querySet._model_cls.table_name(),
        conditions,
    )
    querySet._database.raw(sql)
    return querySet


class ChClient:
    def __init__(self):
        # @see https://github.com/Infinidat/infi.clickhouse_orm/blob/develop/docs/toc.md
        self.db = Database(
            GLOBAL_CONF.CH_DB_NAME,
            db_url=f"http://{GLOBAL_CONF.CH_HOST}:{GLOBAL_CONF.CH_PORT}",
            username=GLOBAL_CONF.CH_USER,
            password=GLOBAL_CONF.CH_PASSWORD,
            timeout=15,
            log_statements=False,
        )
        self.db.add_setting("distributed_ddl_task_timeout", 300)
        self.db.add_setting("distributed_product_mode", "global")
        # 数仓DB
        self.dw_db = Database(
            GLOBAL_CONF.CH_DW_DB_NAME,
            db_url=f"http://{GLOBAL_CONF.CH_HOST}:{GLOBAL_CONF.CH_PORT}",
            username=GLOBAL_CONF.CH_USER,
            password=GLOBAL_CONF.CH_PASSWORD,
            timeout=15,
            log_statements=True,
        )
        self.dw_db.add_setting("distributed_ddl_task_timeout", 300)
        self.dw_db.add_setting("distributed_product_mode", "global")

    def close(self):
        pass

    @retry(
        stop=stop_after_attempt(3),
        retry=retry_if_exception_type(
            (
                requests.exceptions.ConnectionError,
                urllib3.exceptions.ProtocolError,
            )
        ),
    )
    def raw_with_retry(self, db: Database, sql: str):
        """
        带重试机制的原生SQL执行

        Args:
            db: 数据库连接对象
            sql: 要执行的SQL语句

        Returns:
            SQL执行结果
        """
        return db.raw(sql)

    def query_to_df(self, db: Database, sql: str) -> pd.DataFrame:
        """批量处理查询结果转换为DataFrame"""
        query_result = db.select(sql)
        return self.batch_to_df(query_result)

    @staticmethod
    def batch_to_df(query_result, batch_size=1000) -> pd.DataFrame:
        """批量处理查询结果转换为DataFrame"""
        if not query_result:
            return pd.DataFrame()

        batches = []
        batch = []

        for row in query_result:
            batch.append(row.to_dict())
            if len(batch) >= batch_size:
                batches.append(pd.DataFrame(batch))
                batch = []

        if batch:
            batches.append(pd.DataFrame(batch))

        return pd.concat(batches, ignore_index=True) if batches else pd.DataFrame()

    def query_creator_user_by_sec_id(self, secret_id: str) -> CreatorUser | None:
        queryset = (
            CreatorUser.objects_in(self.db)
            .filter(CreatorUser.secret_id == secret_id)
            .order_by("-crawl_time")
        )
        if queryset.count() == 0:
            return None
        else:
            return queryset[0]  # type: ignore

    def query_user_by_sec_uid(self, sec_uid: str) -> User | None:
        queryset = (
            User.objects_in(self.db)
            .filter(User.sec_uid == sec_uid)
            .order_by("-crawl_time")
        )
        if queryset.count() == 0:
            return None
        else:
            return queryset[0]  # type: ignore

    def query_comments_by_digg_count_desc(
        self, sec_uid: str, duration: int
    ) -> List[Comment]:
        queryset = (
            Comment.objects_in(self.db)
            .only("cid", "text", "digg_count", "create_time")
            .distinct()
            .filter(
                (Comment.sec_uid == sec_uid)
                & (Comment.create_time > (int(time.time()) - 3600 * 24 * duration))
            )
            .order_by("-digg_count")
        )
        return [c for c in queryset[:5]]

    def query_comments_by_video_ids(
        self, video_ids: List[str], duration: int
    ) -> List[Comment]:
        if not video_ids:
            return []
        queryset = (
            Comment.objects_in(self.db)
            .only("cid", "text", "digg_count", "create_time")
            .distinct()
            .filter(
                (Comment.aweme_id.isIn(video_ids))
                & (Comment.create_time > (int(time.time()) - 3600 * 24 * duration))
            )
            .order_by("-digg_count")
        )
        return [c for c in queryset[:5]]

    # /douyin相关接口用
    def get_comment_count(self, secret_id) -> int:
        queryset = (
            Video.objects_in(self.db)
            .filter(Video.author_sec_uid == secret_id)
            .aggregate(comment_count=F.sum(Video.comment_count))
        )
        return queryset[0].comment_count  # type: ignore

    @staticmethod
    def get_datetime_before(days: int) -> datetime:
        return datetime.now() - timedelta(days=days)

    def search_video(self, keyword) -> DouhotVideoInfo | None:
        video_desc = JSONExtractString(
            DouhotVideoInfo.video_basic_info, "item_model", "item_title"
        )
        queryset = (
            DouhotVideoInfo.objects_in(self.db)
            .filter(
                DouhotVideoInfo.crawl_time
                >= self.get_datetime_before(CRAWL_DAYS_BEFORE // 3)
            )
            .filter(F.like(video_desc, f"%{keyword}%"))
            .only(
                "author_sec_id",
                "video_id",
                "video_oss_url",
                "cover_oss_url",
                "avatar_oss_url",
                "video_basic_info",
                "video_trends_basic",
                "crawl_from",
                "sub_type",
                "tag",
                "create_time",
                "digg_count",
            )
            .order_by("-digg_count")
        )
        if queryset.count() == 0:
            return None
        else:
            video = queryset[0]
            video.derive_fields()  # type: ignore
            return video  # type: ignore

    # 取前300条标注过的热门视频ids
    def search_hot_marked_video_ids(self) -> List[str]:
        # 7天内的热门视频
        queryset = (
            VideoMark.objects_in(self.db)
            .filter(VideoMark.create_time > (int(time.time()) - 3600 * 24 * 7))
            .only("aweme_id")
            .distinct()
            .order_by("-digg_count")
        )
        return [v.aweme_id for v in queryset][:300]

    def search_hot_videos_by_ids(self, video_ids) -> List[DouhotVideoInfo]:
        queryset = DouhotVideoInfo.objects_in(self.db)
        queryset = (
            self.aggregate_douhot_video_info_by_video_id(queryset)
            .filter(DouhotVideoInfo.video_id.isIn(video_ids))
            .order_by("-digg_count_max")
        )
        return self.convert_douhot_video_infos(queryset)

    # 朋友圈新推荐来源：视频榜视频 (7天内爬取的作品（作品创建时间在10天内）)
    def get_recommend_videos_from_billboard(self) -> List[DouhotVideoInfo]:
        queryset = DouhotVideoInfo.objects_in(self.db)
        queryset = (
            self.aggregate_douhot_video_info_by_video_id(queryset)
            .filter(
                DouhotVideoInfo.create_time >= (int(time.time()) - 3600 * 24 * 10),
                DouhotVideoInfo.crawl_from == CrawlFromType.douhot_video,
            )
            .order_by("-digg_count_max")
        )
        return self.convert_douhot_video_infos(queryset, 300)

    # tools自测用
    def search_video_by_embedding(self, embedding: List[float]) -> List[Video]:
        qs = (
            Video.objects_in(self.db)
            .filter(Video.text_embedding != [])
            .aggregate(
                Video.aweme_id,
                Video.content,
                Video.cover_url,
                Video.desc,
                Video.score,
                Video.digg_count,
                Video.cover_oss_url,
                distance=F("L2Distance", Video.text_embedding, embedding),
            )
            .order_by("distance")
        )
        qs._grouping_fields = None  # type: ignore
        result = qs[:50]
        videos = []
        video_id_set = set()
        for item in result:
            if item.aweme_id in video_id_set:
                continue
            video = Video()
            video.aweme_id = item.aweme_id
            video_id_set.add(video.aweme_id)
            video.content = item.content
            video.cover_url = item.cover_url
            video.desc = item.desc
            video.distance = item.distance
            video.score = item.score  # type: ignore
            video.digg_count = item.digg_count
            video.cover_oss_url = item.cover_oss_url
            if video.score is None:
                video.score = 0  # type: ignore
            video.play_url = video.get_play_url()
            videos.append(video)
        return videos

    def search_video_by_id(self, aweme_id: str) -> DouhotVideoInfo | None:
        queryset = VideoMark.objects_in(self.db).filter(VideoMark.aweme_id == aweme_id)
        if queryset.count() == 0:
            return None

        return self.search_douhot_video_info(aweme_id)

    def search_video_by_aweme_id(self, aweme_id: str) -> DouhotVideoInfo | None:
        return self.search_douhot_video_info(aweme_id)

    def search_video_mark_by_aweme_id(self, aweme_id: str) -> VideoMark | None:
        queryset = VideoMark.objects_in(self.db).filter(VideoMark.aweme_id == aweme_id)
        if queryset.count() == 0:
            return None
        return queryset[0]  # type: ignore

    def search_video_mark_with_disassemble_by_aweme_id(
        self, aweme_id: str
    ) -> VideoMark | None:
        queryset = VideoMark.objects_in(self.db).filter(
            VideoMark.aweme_id == aweme_id, F.isNotNull(VideoMark.disassemble)
        )
        if queryset.count() == 0:
            return None
        return queryset[0]  # type: ignore

    # deprecated：旧标注系统用
    def get_today_hot_videos(self) -> List[Video]:
        queryset = VideoMark.objects_in(self.db).only("aweme_id").distinct()
        marked_aweme_id_list = [v.aweme_id for v in queryset]

        queryset = (
            Video.objects_in(self.db)
            .filter(
                Video.aweme_id.isNotIn(marked_aweme_id_list)
                & (Video.create_time > (int(time.time()) - 3600 * 24))
                & (Video.author_sec_uid != "")
            )
            .order_by("-digg_count")
        )
        res = []
        for video in queryset:
            video.play_url = video.get_play_url()
            res.append(video)
        return res

    def search_videos_by_sec_uids(
        self, sec_uids: List[str], duration="1"
    ) -> List[DouhotVideoInfo]:
        if len(sec_uids) == 0:
            return []

        after_time = int(time.time()) - 3600 * 24 * 7
        if duration == "2":
            after_time = int(time.time()) - 3600 * 24 * 30

        queryset = DouhotVideoInfo.objects_in(self.db).filter(
            DouhotVideoInfo.author_sec_id.isIn(sec_uids)
            & (DouhotVideoInfo.create_time > after_time)
        )
        queryset = self.aggregate_douhot_video_info_by_video_id(queryset).order_by(
            "-create_time_max"
        )
        return self.convert_douhot_video_infos(queryset)

    def search_videos_by_video_ids(
        self, video_ids: List[str], duration: str
    ) -> List[DouhotVideoInfo]:
        if len(video_ids) == 0:
            return []

        after_time = int(time.time()) - 3600 * 24 * 7
        if duration == "2":
            after_time = int(time.time()) - 3600 * 24 * 30

        queryset = DouhotVideoInfo.objects_in(self.db).filter(
            DouhotVideoInfo.video_id.isIn(video_ids),
            DouhotVideoInfo.create_time > after_time,
        )
        queryset = self.aggregate_douhot_video_info_by_video_id(queryset).order_by(
            "-create_time_max"
        )
        return self.convert_douhot_video_infos(queryset)

    def search_page_videos_by_sec_uids(
        self, sec_uids: List[str], start: int = 0, end: int = 0
    ) -> List[DouhotVideoInfo]:
        if len(sec_uids) == 0:
            return []

        if start <= 0:
            earliest_time = datetime.now() - timedelta(days=60)
            start = int(earliest_time.timestamp())
        queryset = DouhotVideoInfo.objects_in(self.db).filter(
            DouhotVideoInfo.author_sec_id.isIn(sec_uids)
            & (DouhotVideoInfo.create_time >= start)
        )
        if end > 0:
            queryset = queryset.filter(DouhotVideoInfo.create_time < end)
        queryset = self.aggregate_douhot_video_info_by_video_id(queryset).order_by(
            "-create_time_max"
        )
        return self.convert_douhot_video_infos(queryset, 20)

    @staticmethod
    def aggregate_douhot_video_info_by_video_id(
        queryset: QuerySet,
        crawl_time_days: int = CRAWL_DAYS_BEFORE,
        include_detail=False,
    ) -> QuerySet:
        detail_info = {}
        if include_detail:
            detail_info = {
                "video_trends_graph": F.argMax(
                    DouhotVideoInfo.video_trends_graph, DouhotVideoInfo.crawl_time
                ),
                "comment_word_cloud": F.argMax(
                    DouhotVideoInfo.comment_word_cloud, DouhotVideoInfo.crawl_time
                ),
                "comment_list": F.argMax(
                    DouhotVideoInfo.comment_list, DouhotVideoInfo.crawl_time
                ),
                "audience_portrait": F.argMax(
                    DouhotVideoInfo.audience_portrait, DouhotVideoInfo.crawl_time
                ),
            }
        # 需要用于过滤或排序的字段，添加了"_max"后缀，以区分原始字段，否则会因混淆而报错
        aggregated_queryset = queryset.aggregate(
            DouhotVideoInfo.video_id,
            author_sec_id_max=F.argMax(
                DouhotVideoInfo.author_sec_id, DouhotVideoInfo.crawl_time
            ),
            video_oss_url=F.argMax(
                DouhotVideoInfo.video_oss_url, DouhotVideoInfo.crawl_time
            ),
            cover_oss_url=F.argMax(
                DouhotVideoInfo.cover_oss_url, DouhotVideoInfo.crawl_time
            ),
            avatar_oss_url=F.argMax(
                DouhotVideoInfo.avatar_oss_url, DouhotVideoInfo.crawl_time
            ),
            video_basic_info=F.argMax(
                DouhotVideoInfo.video_basic_info, DouhotVideoInfo.crawl_time
            ),
            video_trends_basic=F.argMax(
                DouhotVideoInfo.video_trends_basic, DouhotVideoInfo.crawl_time
            ),
            crawl_from_max=F.argMax(
                DouhotVideoInfo.crawl_from, DouhotVideoInfo.crawl_time
            ),
            sub_type_max=F.argMax(DouhotVideoInfo.sub_type, DouhotVideoInfo.crawl_time),
            tag_max=F.argMax(DouhotVideoInfo.tag, DouhotVideoInfo.crawl_time),
            create_time_max=F.argMax(
                DouhotVideoInfo.create_time, DouhotVideoInfo.crawl_time
            ),
            digg_count_max=F.argMax(
                DouhotVideoInfo.digg_count, DouhotVideoInfo.crawl_time
            ),
            **detail_info,
        )
        if crawl_time_days > 0:
            aggregated_queryset = aggregated_queryset.filter(
                DouhotVideoInfo.crawl_time
                >= ChClient.get_datetime_before(crawl_time_days)
            )
        return aggregated_queryset

    @staticmethod
    def convert_douhot_video_infos(
        queryset: QuerySet, limit: int = -1
    ) -> List[DouhotVideoInfo]:
        videos = []
        if limit >= 0:
            queryset = queryset[:limit]
        for video in queryset:
            video.author_sec_id = video.author_sec_id_max
            video.crawl_from = video.crawl_from_max
            video.sub_type = video.sub_type_max
            video.tag = video.tag_max
            video.create_time = video.create_time_max
            video.digg_count = video.digg_count_max
            DouhotVideoInfo.derive_fields(video)  # type: ignore
            videos.append(video)
        return videos

    # deprecated: 通过id查询历史爬取的视频数据（按天汇聚）(由旧的关注视频详情接口调用)
    def search_videos_by_video_id(self, video_id: str) -> List[Video]:
        queryset = (
            Video.objects_in(self.db)
            .filter(Video.aweme_id == video_id)
            .only(
                "aweme_id",
                "content",
                "cover_url",
                "cover_oss_url",
                "create_time",
                "digg_count",
                "author_user_id",
                "author_sec_uid",
                "author_nickname",
                "author_avatar_oss_url",
                "comment_count",
                "share_count",
                "play_count",
                "collect_count",
                "crawl_time",
                "author",
                "desc",
            )
            .order_by("-crawl_time")
        )
        videos = []
        dateSet = set()
        for video in queryset:
            # 判断crawl_time所属日期不能重复
            if video.crawl_time.strftime("%Y-%m-%d") in dateSet:
                continue
            dateSet.add(video.crawl_time.strftime("%Y-%m-%d"))
            video.play_url = video.get_play_url()
            videos.append(video)
        return videos

    def search_creator_videos_by_video_id(self, video_id: str) -> List[CreatorVideo]:
        queryset = (
            CreatorVideo.objects_in(self.db)
            .filter(CreatorVideo.video_id == video_id)
            .order_by("-crawl_time")
        )
        return [c for c in queryset]

    def search_video_ids_by_sec_uid(self, sec_uid, duration) -> List[str]:
        after_time = int(time.time()) - 3600 * 24 * duration

        queryset = (
            DouhotVideoInfo.objects_in(self.db)
            .only("video_id")
            .distinct()
            .filter(
                (DouhotVideoInfo.author_sec_id == sec_uid)
                & (DouhotVideoInfo.create_time >= after_time)
                & (
                    DouhotVideoInfo.crawl_time
                    >= self.get_datetime_before(CRAWL_DAYS_BEFORE // 3)
                )
            )
        )
        return [v.video_id for v in queryset]

    def get_douhot_video_comment_by_ids(
        self, video_id_list: List[str] | None = None
    ) -> List[DouhotVideoInfo]:
        qs = (
            DouhotVideoInfo.objects_in(self.db)
            .aggregate(
                DouhotVideoInfo.video_id,
                comment_list=F.argMax(
                    DouhotVideoInfo.comment_list, DouhotVideoInfo.crawl_time
                ),
            )
            .filter(F.isIn(DouhotVideoInfo.video_id, video_id_list))  # type: ignore
        )
        return [item for item in qs]

    def is_video_marked(self, aweme_id: str) -> bool:
        queryset = VideoMark.objects_in(self.db).filter(VideoMark.aweme_id == aweme_id)
        return queryset.count() > 0

    def insert_video(self, video: Video):
        return self.db.insert([video])

    def insert_video_to_mark(self, video: VideoToMark):
        return self.db.insert([video])

    def insert_video_marked(self, video: VideoMark):
        return self.db.insert([video])

    def get_today_hot_mark_videos(self) -> List[VideoMark]:
        queryset = (
            VideoMark.objects_in(self.db)
            .only(
                "aweme_id",
                "cover_url",
                "cover_oss_url",
                "create_time",
                "desc",
                "digg_count",
                "author_user_id",
                "author_sec_uid",
                "author_nickname",
                "author_avatar_oss_url",
            )
            .distinct()
            .filter(
                (VideoMark.create_time > (int(time.time()) - 3600 * 24))
                & (VideoMark.author_sec_uid != "")
            )
            .order_by("-mark_score")
        )

        videos = []
        video_id_set = set()
        for video in queryset:
            if video.aweme_id in video_id_set:
                continue
            video_id_set.add(video.aweme_id)
            video.play_url = video.get_play_url()
            videos.append(video)
        return videos

    def search_mark_video_by_embedding_type(
        self,
        embedding: List[float],
        embedding_type: str,
        limit=3,
        allow_artificial=False,
    ) -> List[VideoMark]:
        # hardcode 2024年6月12日之前的数据不需要
        embedding_field = getattr(VideoMark, embedding_type)
        qs = (
            VideoMark.objects_in(self.db)
            .filter(
                (embedding_field != [])
                & (VideoMark.mark_time > 1718174862)
                & (
                    F.ifNull(VideoMark.audit_result, VIDEO_MARK_AUDIT_RESULT_PASS)
                    != VIDEO_MARK_AUDIT_RESULT_FAIL
                )
            )
            .aggregate(
                VideoMark.aweme_id,
                VideoMark.author_sec_uid,
                VideoMark.desc,
                VideoMark.content,
                VideoMark.mark_highlights,
                VideoMark.cover_oss_url,
                VideoMark.cover_url,
                VideoMark.digg_count,
                VideoMark.mark_score,
                VideoMark.author_topic_keywords,
                VideoMark.author_eye_catching_points,
                VideoMark.author_opening_quality_score,
                VideoMark.disassemble,
                distance=F("L2Distance", embedding_field, embedding),
            )
            .order_by("distance")
        )
        # 去掉group by，这里aggregate只是为了AS
        qs._grouping_fields = None  # type: ignore
        if not allow_artificial:
            qs = qs.filter(VideoMark.source != VIDEO_SOURCE_ARTIFICIAL)

        result = qs[:limit]
        videos = []
        video_id_set = set()
        for item in result:
            if item.aweme_id in video_id_set:
                continue
            video = VideoMark()
            video.aweme_id = item.aweme_id
            video.author_sec_uid = item.author_sec_uid
            video_id_set.add(video.aweme_id)
            video.content = item.content  # GPTAnnotation.description
            video.mark_highlights = item.mark_highlights
            video.cover_oss_url = item.cover_oss_url
            video.cover_url = item.cover_url
            video.desc = item.desc
            video.digg_count = item.digg_count
            video.distance = item.distance
            video.mark_score = item.mark_score
            video.author_topic_keywords = item.author_topic_keywords
            video.author_eye_catching_points = item.author_eye_catching_points
            video.author_opening_quality_score = item.author_opening_quality_score
            video.disassemble = item.disassemble
            video.source = item.source
            video.play_url = video.get_play_url()
            videos.append(video)
        return videos

    async def search_mark_video_by_embedding(
        self, embedding: List[float]
    ) -> List[VideoMark]:
        """
        通过视频的简介、内容、标签等embedding向量进行相似度搜索
        """
        tasks = [
            self.search_mark_video_by_embedding_type(
                embedding=embedding, embedding_type="mark_description_embedding"
            ),
            self.search_mark_video_by_embedding_type(
                embedding=embedding, embedding_type="mark_highlights_embedding"
            ),
            self.search_mark_video_by_embedding_type(
                embedding=embedding, embedding_type="mark_script_embedding"
            ),
            self.search_mark_video_by_embedding_type(
                embedding=embedding, embedding_type="mark_keywords_embedding"
            ),
        ]
        results = await asyncio.gather(*tasks)  # type: ignore
        videos = []
        video_id_set = set()
        for result in results:
            for video in result:
                if video.aweme_id in video_id_set:
                    continue
                video_id_set.add(video.aweme_id)
                videos.append(video)
        sorted_list = sorted(videos, key=lambda x: x.mark_score, reverse=True)
        if len(sorted_list) > 3:
            results = sorted_list[0:3]
        return results

    def search_mark_video_by_aweme_ids(self, aweme_ids: List[str]) -> List[VideoMark]:
        """从 video_mark 表中查询视频数据。该表中的数据都是经过人工审核的"""
        if len(aweme_ids) == 0:
            return []

        qs = VideoMark.objects_in(self.db).filter(
            F.isIn(Video.aweme_id, aweme_ids)  # type: ignore
            & (VideoMark.mark_time > 1718174862)
            & (
                F.ifNull(VideoMark.audit_result, VIDEO_MARK_AUDIT_RESULT_PASS)
                != VIDEO_MARK_AUDIT_RESULT_FAIL
            )
        )
        videos: List[VideoMark] = []
        video_id_set = set()
        for item in qs:
            if item.aweme_id in video_id_set:
                continue
            video_id_set.add(item.aweme_id)

            video = VideoMark()
            video.aweme_id = item.aweme_id
            video.create_time = item.create_time
            video.author_sec_uid = item.author_sec_uid
            video_id_set.add(video.aweme_id)
            video.content = item.content  # GPTAnnotation.description
            video.mark_description = item.mark_description
            video.mark_highlights = item.mark_highlights
            video.cover_oss_url = item.cover_oss_url
            video.cover_url = item.cover_url
            video.desc = item.desc
            video.digg_count = item.digg_count
            video.distance = 0
            video.mark_score = item.mark_score
            video.author_topic_keywords = item.author_topic_keywords
            video.author_eye_catching_points = item.author_eye_catching_points
            video.author_opening_quality_score = item.author_opening_quality_score
            video.disassemble = item.disassemble
            video.source = item.source
            video.play_url = video.get_play_url()
            video.tag = item.tag
            video.structured_disassemble = item.structured_disassemble
            videos.append(video)

        videos = sorted(videos, key=lambda x: x.distance, reverse=False)  # type: ignore
        return videos

    def search_video_mark_by_aweme_ids(self, aweme_ids: List[str]) -> List[VideoMark]:
        if not aweme_ids:
            return []
        queryset = VideoMark.objects_in(self.db).filter(
            VideoMark.aweme_id.isIn(tuple(aweme_ids))
        )
        return list(queryset)

    def search_marketing_video(
        self, limit=3, allow_artificial=False
    ) -> List[VideoMark]:
        tags = ["短视频运营", "抖音运营", "视频营销", "营销策略", "营销思维"]

        # hardcode 2024年6月12日之前的数据不需要
        qs = VideoMark.objects_in(self.db).filter(
            (VideoMark.mark_time > 1718174862)
            & (
                F.ifNull(VideoMark.audit_result, VIDEO_MARK_AUDIT_RESULT_PASS)
                != VIDEO_MARK_AUDIT_RESULT_FAIL
            )
        )
        qs = qs.filter(
            F.hasAny(F.splitByChar(",", F.ifNull(VideoMark.mark_keywords, "")), tags)
            | F.hasAny(
                F.splitByChar(",", F.ifNull(VideoMark.author_topic_keywords, "")), tags
            )
        )
        if not allow_artificial:
            qs = qs.filter(VideoMark.source != VIDEO_SOURCE_ARTIFICIAL)
        qs = qs.order_by("-mark_score")
        result = qs[:limit]
        videos = []
        video_id_set = set()
        for item in result:
            if item.aweme_id in video_id_set:
                continue
            video = VideoMark()
            video.aweme_id = item.aweme_id
            video.author_sec_uid = item.author_sec_uid
            video_id_set.add(video.aweme_id)
            video.content = item.content
            video.mark_highlights = item.mark_highlights
            video.cover_oss_url = item.cover_oss_url
            video.cover_url = item.cover_url
            video.desc = item.desc
            video.digg_count = item.digg_count
            video.distance = 0.0
            video.mark_score = item.mark_score
            video.author_topic_keywords = item.author_topic_keywords
            video.author_eye_catching_points = item.author_eye_catching_points
            video.author_opening_quality_score = item.author_opening_quality_score
            video.disassemble = item.disassemble
            video.source = item.source
            video.play_url = video.get_play_url()
            videos.append(video)
        return videos

    def search_mark_video_by_embedding_sync(
        self, embedding: List[float], allow_artificial: bool = False
    ) -> List[VideoMark]:
        desc_results = self.search_mark_video_by_embedding_type(
            embedding=embedding,
            embedding_type="mark_description_embedding",
            limit=26,
            allow_artificial=allow_artificial,
        )
        # highlight_results = self.search_mark_video_by_embedding_type(embedding=embedding, embedding_type="mark_highlights_embedding", limit=25, allow_artificial=allow_artificial)
        # script_results = self.search_mark_video_by_embedding_type(embedding=embedding, embedding_type="mark_script_embedding", limit=25, allow_artificial=allow_artificial)
        keyword_results = self.search_mark_video_by_embedding_type(
            embedding=embedding,
            embedding_type="mark_keywords_embedding",
            limit=26,
            allow_artificial=allow_artificial,
        )
        results = []
        results.extend(desc_results)
        # results.extend(highlight_results)
        # results.extend(script_results)
        results.extend(keyword_results)
        videos = []
        video_id_set = set()
        for video in results:
            if video.aweme_id in video_id_set:
                continue
            video_id_set.add(video.aweme_id)
            videos.append(video)
        sorted_list = sorted(videos, key=lambda x: x.distance, reverse=False)
        return sorted_list

    # 查询需要进行人工审核的视频列表（一条视频可能出现两条数据：视频总结与up主总结，因此代码中进行了合并处理）
    def search_one_video_to_mark(self) -> VideoToMark | None:
        queryset = (
            VideoToMark.objects_in(self.db)
            .filter(
                (VideoToMark.annotate_time >= int(time.time() - 86400))
                & (VideoToMark.marked == False)
            )
            .only(VideoToMark.aweme_id)
            .distinct()
            .order_by(F.rand())
        )

        # 优先推送热点宝的视频，虽然没有up总结，但是热点宝的更可能通过标注
        douhot_queryset = queryset.filter(F.like(VideoToMark.annotation, "%热点宝%"))
        if douhot_queryset.count() > 0:
            aweme_id = douhot_queryset[0].aweme_id  # type: ignore
            return self.search_video_to_mark_by_aweme_id(aweme_id)
        # 没有热点宝的话，就随机推送一个，这时候来源是recommend crawler被推送的视频里经过筛选的，包含up总结
        if queryset.count() == 0:
            return None

        aweme_id = queryset[0].aweme_id  # type: ignore
        return self.search_video_to_mark_by_aweme_id(aweme_id)

    def search_video_to_mark_by_aweme_id(self, aweme_id: str) -> VideoToMark | None:
        queryset = (
            VideoToMark.objects_in(self.db)
            .filter((VideoToMark.aweme_id == aweme_id) & (VideoToMark.marked == False))
            .order_by("-update_time")
        )
        if queryset.count() == 0:
            return None
        res: VideoToMark = None  # type: ignore
        # 取前两条数据
        for v in queryset[:3]:
            if res is None:
                res = v
                res.play_url = "https://www.douyin.com/video/" + v.aweme_id
                res.gpt_annotation = ""  # type: ignore
                res.gpt_creator_annotation = ""  # type: ignore
                res.gpt_disassemble = ""  # type: ignore

            if v.annotate_type == "gpt_annotation":
                res.gpt_annotation = v.annotation
            elif v.annotate_type == "gpt_creator_annotation":
                res.gpt_creator_annotation = v.annotation
            elif v.annotate_type == "gpt_disassemble":
                res.gpt_disassemble = v.annotation
        return res

    def set_video_marked(self, aweme_id: str):
        qs = VideoToMark.objects_in(self.db).filter(VideoToMark.aweme_id == aweme_id)
        update_on_distributed_table(qs, marked=True)

    def search_marked_videos(self) -> List[VideoMark]:
        queryset = (
            VideoMark.objects_in(self.db)
            .filter(VideoMark.mark_time > (int(time.time()) - 3600 * 24))
            .order_by("-mark_time")
        )
        return [v for v in queryset]

    # deprecated: recommender使用
    def search_videos_by_day(self, day: int) -> List[Video]:
        queryset = (
            Video.objects_in(self.db)
            .filter(Video.create_time > (int(time.time()) - 3600 * 24 * day))
            .order_by("-digg_count")
        )
        return [v for v in queryset]

    def search_marked_videos_to_audit(self) -> List[VideoMark]:
        queryset = (
            VideoMark.objects_in(self.db)
            .filter(
                (VideoMark.mark_time > 1718174862) & F.isNull(VideoMark.audit_result)
            )
            .only(
                VideoMark.aweme_id,
                VideoMark.mark_score,
                VideoMark.mark_keywords,
                VideoMark.author_opening_quality_score,
                VideoMark.author_topic_keywords,
                VideoMark.mark_user,
                VideoMark.mark_time,
            )
            .order_by("-mark_time")
        )
        return [v for v in queryset]

    def search_marked_video_by_aweme_id(self, aweme_id: str) -> VideoMark | None:
        queryset = (
            VideoMark.objects_in(self.db)
            .filter(VideoMark.aweme_id == aweme_id)
            .order_by("-mark_time")
        )
        if queryset.count() == 0:
            return None
        return queryset[0]  # type: ignore

    def audit_video(self, aweme_id: str, reject: bool):
        qs = VideoMark.objects_in(self.db).filter(VideoMark.aweme_id == aweme_id)
        update_on_distributed_table(
            qs,
            audit_result=(
                VIDEO_MARK_AUDIT_RESULT_FAIL if reject else VIDEO_MARK_AUDIT_RESULT_PASS
            ),
        )

    def reject_video_for_reedit(self, aweme_id: str):
        """
        打回已经审核的标注，但是允许修改后再审核
        """
        # 先删掉已经入库的数据
        qs = VideoMark.objects_in(self.db).filter(VideoMark.aweme_id == aweme_id)
        delete_on_distributed_table(qs)
        # 把视频修改为未审核状态
        qs = VideoToMark.objects_in(self.db).filter(VideoToMark.aweme_id == aweme_id)
        update_on_distributed_table(qs, marked=False)

    # deprecated：旧标注系统和工具脚本使用
    def clear_artificial_video(self, aweme_id: str):
        if not aweme_id.startswith(VIDEO_SOURCE_ARTIFICIAL):
            raise RuntimeError("only artificial could be cleared by this function")

        qs = Video.objects_in(self.db).filter(Video.aweme_id == aweme_id)
        delete_on_distributed_table(qs)

        qs = VideoToMark.objects_in(self.db).filter(VideoToMark.aweme_id == aweme_id)
        delete_on_distributed_table(qs)

        qs = VideoMark.objects_in(self.db).filter(VideoMark.aweme_id == aweme_id)
        delete_on_distributed_table(qs)

    def search_daren_by_sec_uid(self, sec_uid: str) -> Daren | None:
        queryset = (
            Daren.objects_in(self.db)
            .filter(Daren.author_sec_uid == sec_uid)
            .order_by("-crawl_time")
        )
        if queryset.count() == 0:
            return None
        else:
            return queryset[0]  # type: ignore

    def comment_sentiment_statistic(self, video_ids: List[str], days: int = 7) -> []:  # type: ignore
        after_time = int(time.time()) - 3600 * 24 * days
        if not video_ids:
            return []
        queryset = (
            Comment.objects_in(self.db)
            .aggregate(
                day=F.toDate(Comment.create_time),
                sentiment=Comment.sentiment,
                count=F.count(),
            )
            .group_by("day", "sentiment")
            .filter(
                (Comment.create_time > after_time) & (Comment.aweme_id.isIn(video_ids))
            )
        )
        # 返回一个json列表
        res = []
        for item in queryset:
            res.append(
                {"day": item.day, "sentiment": item.sentiment, "count": item.count}
            )
        return res

    def insert_user_action_data(
        self,
        user_id: int,
        action_type: int,
        target_type: int,
        target_id: str,
        action_value: int,
    ):
        user_action_data = UserActionData(
            user_id=user_id,
            action_type=action_type,
            target_type=target_type,
            target_id=target_id,
            action_value=action_value,
            create_time=int(time.time()),
        )
        return self.db.insert([user_action_data])

    def insert_user_like_action_data(
        self, user_id: int, target_id: str, action_value: int
    ):
        user_action_data = UserActionData(
            user_id=user_id,
            action_type=UserActionType.like.value,
            target_type=UserActionTarget.video.value,
            target_id=target_id,
            action_value=action_value,
            create_time=int(time.time()),
        )
        return self.db.insert([user_action_data])

    def insert_user_follow_action_data(
        self, user_id: int, target_id: str, action_value: int
    ):
        user_action_data = UserActionData(
            user_id=user_id,
            action_type=UserActionType.follow.value,
            target_type=UserActionTarget.account.value,
            target_id=target_id,
            action_value=action_value,
            create_time=int(time.time()),
        )
        return self.db.insert([user_action_data])

    def insert_user_follow_video_action_data(
        self, user_id: int, target_id: str, action_value: int
    ):
        user_action_data = UserActionData(
            user_id=user_id,
            action_type=UserActionType.follow.value,
            target_type=UserActionTarget.video.value,
            target_id=target_id,
            action_value=action_value,
            create_time=int(time.time()),
        )
        return self.db.insert([user_action_data])

    def search_douhot_rank(
        self, douhot_type: str, douhot_sentence_tag: str
    ) -> (dict, str):  # type: ignore
        queryset = (
            DouhotRank.objects_in(self.db)
            .filter(
                DouhotRank.douhot_type == douhot_type,
                DouhotRank.douhot_sentence_tag == douhot_sentence_tag,
            )
            .order_by("-crawl_time")
        )

        if queryset.count() == 0:
            return {}, ""
        else:
            return queryset[0].json_data, queryset[0].last_update_time.strftime(  # type: ignore
                "%Y-%m-%d %H:%M:%S"
            )

    # 查询热点宝账号列表(account_trends_basic字段数据累计), 根据sec_uid查询时间范围内的数据，每天只取一条
    def search_douhot_account_list(
        self, sec_uids: List[str], duration: str
    ) -> List[DouhotAccountInfo]:
        start_time = int(time.time()) - 3600 * 24 * (7 if duration == "1" else 30)

        # 给in条件的字符串加上引号
        sec_uids = [f"'{sec_uid}'" for sec_uid in sec_uids]

        query = f"""SELECT
                sec_uid,
                author_info,
                account_trends_basic,
                account_trends_graph,
                product_data,
                product_list,
                fans_portrait,
                fans_interest_similar,
                fans_interest_topic,
                fans_interest_search,
                crawl_time
            FROM
            (
                SELECT
                    *,
                    row_number() OVER (PARTITION BY sec_uid ORDER BY crawl_time DESC) AS rn
                FROM
                    douhot_account_info
                WHERE
                    sec_uid IN ({','.join(map(str, sec_uids))}) AND
                    crawl_time > {start_time}
            )
            WHERE
                rn = 1
            ORDER BY
                crawl_time DESC"""
        result = list(self.db.select(query, model_class=DouhotAccountInfo))
        return result

    # 查询热点宝账号详情
    def search_douhot_account_info(self, sec_uid: str) -> DouhotAccountInfo | None:
        queryset = (
            DouhotAccountInfo.objects_in(self.db)
            .filter(DouhotAccountInfo.sec_uid == sec_uid)
            .order_by("-crawl_time")
        )
        if queryset.count() == 0:
            return None
        return queryset[0]  # type: ignore

    # 查询热点宝视频信息； 根据aweme_id查询最新爬取的数据
    def search_douhot_video_info(self, video_id: str) -> DouhotVideoInfo | None:
        # 第一步：只查询基本字段和crawl_time，找到最新的记录
        queryset_basic = (
            DouhotVideoInfo.objects_in(self.db)
            .filter(DouhotVideoInfo.video_id == video_id)
            .only("video_id", "crawl_time")
            .order_by("-crawl_time")
        )
        if queryset_basic.count() == 0:
            return None

        # 获取最新记录的crawl_time
        latest_record = queryset_basic[0]
        latest_crawl_time = latest_record.crawl_time  # type: ignore

        # 第二步：根据具体的crawl_time查询完整数据
        queryset_full = DouhotVideoInfo.objects_in(self.db).filter(
            DouhotVideoInfo.video_id == video_id,
            DouhotVideoInfo.crawl_time == latest_crawl_time,
        )
        if queryset_full.count() == 0:
            return None

        douhot_video = queryset_full[0]
        douhot_video.derive_fields()  # type: ignore
        return douhot_video  # type: ignore

    def search_tiktok_video_info(self, video_id: str) -> TiktokVideoInfo | None:
        queryset = (
            TiktokVideoInfo.objects_in(self.db)
            .filter(TiktokVideoInfo.video_id == video_id)
            .order_by("-crawl_time")
        )
        if queryset.count() == 0:
            return None
        tiktok_video = queryset[0]  # type: ignore
        return tiktok_video  # type: ignore

    def search_nox_video_info(
        self, video_id: str
    ) -> EtlNoxProductVideoList | EtlNoxDarenVideoList | None:
        queryset = (
            EtlNoxProductVideoList.objects_in(self.db)
            .filter(EtlNoxProductVideoList.video_id == video_id)
            .order_by("-update_time")
        )
        if queryset.count() != 0:
            return queryset[0]  # type: ignore

        queryset = (
            EtlNoxDarenVideoList.objects_in(self.db)
            .filter(EtlNoxDarenVideoList.video_id == video_id)
            .order_by("-update_time")
        )
        if queryset.count() != 0:
            return queryset[0]  # type: ignore
        return None  # type: ignore

    # 根据video_ids查询视频数据，每个id只取最新爬取的一条
    def search_douhot_videos_by_ids(
        self,
        video_ids: List[str],
        include_detail=False,
        crawl_time_days: int = CRAWL_DAYS_BEFORE,
    ) -> List[DouhotVideoInfo] | None:
        if not video_ids:
            return []
        queryset = DouhotVideoInfo.objects_in(self.db).filter(
            DouhotVideoInfo.video_id.isIn(tuple(video_ids))
        )
        queryset = self.aggregate_douhot_video_info_by_video_id(
            queryset, include_detail=include_detail, crawl_time_days=crawl_time_days
        )
        videos = self.convert_douhot_video_infos(queryset)
        if len(videos) != len(video_ids):
            got = [video.video_id for video in videos]
            missing = set(video_ids).difference(got)
            logger.warning(
                f"查得{len(videos)}/{len(video_ids)}条视频，未查到：{missing}"
            )
        return videos

    def search_douhot_sentence_video_list(self, sentence_id: str) -> List[str]:
        queryset = (
            DouhotSentenceInfo.objects_in(self.db)
            .only(DouhotSentenceInfo.video_list)
            .filter(
                DouhotSentenceInfo.sentence_id == sentence_id,
                DouhotSentenceInfo.video_list != "[]",
            )
            .order_by("-crawl_time")
        )
        if queryset.count() == 0:
            return []
        # video_list为字符串类型的数组，需要转换为list
        return json.loads(queryset[0].video_list)  # type: ignore

    def search_douhot_sentence(self, sentence_id: str) -> DouhotSentenceInfo | None:
        queryset = (
            DouhotSentenceInfo.objects_in(self.db)
            .filter(
                DouhotSentenceInfo.sentence_id == sentence_id,
                DouhotSentenceInfo.video_list != "[]",
            )
            .order_by("-crawl_time")
        )
        if queryset.count() == 0:
            return None
        return queryset[0]  # type: ignore

    def search_douhot_topic_video_list(self, topic_id: str) -> List[str]:
        queryset = (
            DouhotTopicInfo.objects_in(self.db)
            .only(DouhotTopicInfo.video_list)
            .filter(
                DouhotTopicInfo.topic_id == topic_id,
                DouhotTopicInfo.video_list != "[]",
            )
            .order_by("-crawl_time")
        )
        if queryset.count() == 0:
            return []
        # video_list为字符串类型的数组，需要转换为list
        return json.loads(queryset[0].video_list)  # type: ignore

    def search_douhot_topic(self, topic_id: str) -> DouhotTopicInfo | None:
        queryset = (
            DouhotTopicInfo.objects_in(self.db)
            .filter(
                DouhotTopicInfo.topic_id == topic_id,
                DouhotTopicInfo.video_list != "[]",
            )
            .order_by("-crawl_time")
        )
        if queryset.count() == 0:
            return None
        return queryset[0]  # type: ignore

    def daren_video_disassemble_check_exist(self, aweme_id: str) -> bool:
        return (
            DarenVideoDisassemble.objects_in(self.db)
            .filter(DarenVideoDisassemble.aweme_id == aweme_id)
            .count()
            > 0
        )

    def daren_video_disassemble_create(self, item: DarenVideoDisassemble):
        return self.db.insert([item])

    def daren_video_disassemble_read(
        self, aweme_id: str
    ) -> DarenVideoDisassemble | None:
        qs = DarenVideoDisassemble.objects_in(self.db).filter(
            DarenVideoDisassemble.aweme_id == aweme_id
        )
        if qs.count() > 0:
            return qs[0]  # type: ignore
        else:
            return None

    def daren_video_disassemble_update(self, aweme_id: str, **kwargs):
        if "update_time" not in kwargs:
            kwargs["update_time"] = int(time.time())
        qs = DarenVideoDisassemble.objects_in(self.db).filter(
            (DarenVideoDisassemble.aweme_id == aweme_id)
        )
        update_on_distributed_table(qs, **kwargs)

    def get_daren_video_disassemble_by_file_hash(
        self, file_hash: str, job_type: str
    ) -> DarenVideoDisassemble | None:
        qs = DarenVideoDisassemble.objects_in(self.db).filter(
            DarenVideoDisassemble.file_hash == file_hash,
            DarenVideoDisassemble.job_type == job_type,
            DarenVideoDisassemble.origin_markdown != "",
        )
        if qs.count() == 0:
            return None
        return qs[0]  # type: ignore

    def get_daren_video_disassemble_by_video_id(
        self, video_id: str, job_type: str
    ) -> DarenVideoDisassemble | None:
        qs = DarenVideoDisassemble.objects_in(self.db).filter(
            DarenVideoDisassemble.aweme_id == video_id,
            DarenVideoDisassemble.job_type == job_type,
            DarenVideoDisassemble.origin_markdown != "",
        )
        if qs.count() == 0:
            return None
        return qs[0]  # type: ignore

    def get_daren_video_disassemble_result(
        self, file_hash: str, disassemble_type: str
    ) -> DarenVideoDisassembleResult | None:
        qs = (
            DarenVideoDisassembleResult.objects_in(self.db)
            .filter(
                DarenVideoDisassembleResult.file_hash == file_hash,
                DarenVideoDisassembleResult.disassemble_type == disassemble_type,
            )
            .order_by("-create_time")
        )
        if qs.count() == 0:
            return None
        return qs[0]  # type: ignore

    def add_daren_video_disassemble_result(self, result: DarenVideoDisassembleResult):
        result.create_time = int(time.time())  # type: ignore
        result.update_time = int(time.time())  # type: ignore
        self.db.insert([result])

    def search_baike_entries(self) -> List[BaikeEntry]:
        return [
            v
            for v in BaikeEntry.objects_in(self.db).filter(
                BaikeEntry.status == "active"
            )
        ]

    def query_fact_douhot_topic_info_by_time(
        self, start_time: datetime, end_time: datetime, offset: int, limit: int
    ) -> list[FactDouhotTopic]:
        return [
            topic
            for topic in FactDouhotTopic.objects_in(self.dw_db)
            .filter(
                (FactDouhotTopic.update_time >= start_time)
                & (FactDouhotTopic.update_time <= end_time)
                & (FactDouhotTopic.hot_score_7d > 0)
            )
            .order_by("update_time")[offset : offset + limit]
        ]

    def query_dim_brand_info_by_page(self, offset: int, limit: int) -> list[DimBrand]:
        return [
            item
            for item in DimBrand.objects_in(self.dw_db)
            .only("brand_name_correct", "brand_alias", "label")
            .order_by("brand_id")[offset : offset + limit]
        ]

    def query_agg_product_info_by_page(
        self, offset: int, limit: int
    ) -> list[AggProductBaseInfo]:

        return [
            item
            for item in AggProductBaseInfo.objects_in(self.dw_db)
            .only(
                "product_series",
                "corrected_product_name",
                "category1",
                "category2",
                "category3",
                "category4",
            )
            .order_by("product_id")[offset : offset + limit]
        ]

    def query_dim_shop_info_by_page(self, offset: int, limit: int) -> list[DimShop]:
        return [
            item
            for item in DimShop.objects_in(self.dw_db)
            .only(
                "shop_name",
                "category1_list",
                "category2_list",
                "category3_list",
                "category4_list",
            )
            .order_by("shop_id")[offset : offset + limit]
        ]

    def delete_embedding_text(
        self, text_type: EmbeddingTextType, test_list: list[EmbeddingTextItem]
    ):
        if len(test_list) == 0:
            return

        cond = Q(EmbeddingText.text == test_list[0].text) & Q(
            EmbeddingText.json_metadata == test_list[0].json_metadata
        )
        for item in test_list[1:]:
            cond1 = Q(EmbeddingText.text == item.text) & Q(
                EmbeddingText.json_metadata == item.json_metadata
            )
            cond = cond | cond1

        qs = (
            EmbeddingText.objects_in(self.dw_db)
            .filter(cond)
            .filter(EmbeddingText.text_type == text_type)
        )

        delete_on_distributed_table(qs)

    def query_embedding_text_by_page(
        self, text_type: EmbeddingTextType, offset: int, limit: int
    ) -> list[EmbeddingText]:
        return [
            item
            for item in EmbeddingText.objects_in(self.dw_db)
            .filter(EmbeddingText.text_type == text_type)
            .only("text_type", "text", "json_metadata")
            .order_by("update_time", "text")[offset : offset + limit]
        ]

    def search_embedding_text(
        self,
        text_type: EmbeddingTextType,
        embedding: List[float],
        threshold: float = 0.96,
        limit=3,
        metadata_dict: Optional[dict[str, Any]] = None,
    ) -> List[EmbeddingTextWithCosineDistance]:
        qs = (
            EmbeddingText.objects_in(self.dw_db)
            .aggregate(
                EmbeddingText.text_type,
                EmbeddingText.text,
                EmbeddingText.json_metadata,
                cosine_distance=(
                    0.03
                    * F.log10(
                        F(
                            "if",
                            F("JSONHas", EmbeddingText.json_metadata, "count"),
                            JSONExtractInt(EmbeddingText.json_metadata, "count"),
                            1,
                        )
                    )
                    + (
                        1
                        - F("cosineDistance", EmbeddingText.embedding_vector, embedding)
                    )
                ),
            )
            .filter(EmbeddingTextWithCosineDistance.cosine_distance > threshold)
            .filter(EmbeddingText.text_type == text_type)
            .order_by("-cosine_distance")[0:limit]
        )

        if metadata_dict:
            for k, v in metadata_dict.items():
                qs = qs.filter(
                    JSONExtractString(EmbeddingText.json_metadata, k) == str(v)
                )

        qs._grouping_fields = None  # type: ignore
        # print(qs.as_sql())

        return [item for item in qs]

    def query_dim_category_info_by_page(
        self, offset: int, limit: int
    ) -> list[DimCategory]:
        return [
            item
            for item in DimCategory.objects_in(self.dw_db)
            .order_by("-depth")
            .order_by("category_id")[offset : offset + limit]
        ]

    def query_structured_video_by_page_and_update_after(
        self, update_after: datetime, offset: int, limit: int
    ) -> list[ETLStructuredDouyinVideo]:
        return [
            item
            for item in ETLStructuredDouyinVideo.objects_in(self.db)
            .filter(ETLStructuredDouyinVideo.update_time > update_after)
            .order_by("update_time", "aweme_id")[offset : offset + limit]
        ]

    def query_agg_video_v2_by_page_and_update_after(
        self, update_after: datetime, offset: int, limit: int
    ) -> list[ModelAggVideoBaseInfoV2]:
        return [
            item
            for item in ModelAggVideoBaseInfoV2.objects_in(self.dw_db)
            .filter(ModelAggVideoBaseInfoV2.update_time > update_after)
            .order_by("update_time", "video_id")[offset : offset + limit]
        ]

    def query_structured_daren_by_page(
        self, offset: int, limit: int
    ) -> list[ETLStructuredDouyinDaren]:
        return [
            item
            for item in ETLStructuredDouyinDaren.objects_in(self.db).order_by(
                "-update_time"
            )[offset : offset + limit]
        ]

    def query_agg_data_by_id(self, daren_id: str) -> ModelAggDarenBasicInfo:  # type: ignore
        queryset = ModelAggDarenBasicInfo.objects_in(self.dw_db).filter(
            ModelAggDarenBasicInfo.daren_id == daren_id
        )
        if queryset.count() > 0:
            return queryset[0]  # type: ignore

    # 电商助手使用，待删除
    def load_video_attributes(self) -> list[DictVideoAttributes]:
        # 现在视频属性不需要过滤。
        queryset = DictVideoAttributes.objects_in(self.dw_db)
        return list(queryset)

    def get_xinhong_account_detail(self, account_id) -> XinhongAccountDetail | None:
        queryset = XinhongAccountDetail.objects_in(self.db).filter(
            XinhongAccountDetail.account_id == account_id
        )
        if queryset.count() == 0:
            return None
        else:
            return queryset[0]  # type: ignore

    def get_xiaohongshu_note_info(self, note_id) -> XiaohongshuNoteInfo | None:
        queryset = XiaohongshuNoteInfo.objects_in(self.db).filter(
            XiaohongshuNoteInfo.note_id == note_id
        )
        if queryset.count() == 0:
            return None
        else:
            return queryset[0]  # type: ignore

    def get_xinhong_account_hottest_history_note(
        self, account_id: str
    ) -> XinhongAccountHistoryNotes | None:
        # 挑选account_id的记录中，hot_info.max_score 值最大的那条记录。只需要hot_note和hot_info字段。
        qs = (
            XinhongAccountHistoryNotes.objects_in(self.db)
            .filter(XinhongAccountHistoryNotes.account_id == account_id)
            .order_by("-JSONExtractFloat(hot_info, 'max_score')")
            .only("hot_note")
        )
        if qs.count() == 0:
            return None
        return qs[0]  # type: ignore

    def query_product_info_by_ids(
        self, product_ids: list[str], order_by: str
    ) -> list[ModelAggProductBaseInfo]:
        if not product_ids:
            return []
        return [
            product
            for product in ModelAggProductBaseInfo.objects_in(self.dw_db)
            .filter(F.isIn(ModelAggProductBaseInfo.product_id, product_ids))  # type: ignore
            .order_by(order_by if order_by else "product_id")
        ]

    def query_recognition_result_info_by_product_id(
        self, product_id_list: list[str], rec_type: str
    ) -> list[ModelProcessProductAIRecognitionResult]:
        query_set = (
            ModelProcessProductAIRecognitionResult.objects_in(self.dw_db)
            .filter(
                F.isIn(
                    ModelProcessProductAIRecognitionResult.product_id, product_id_list  # type: ignore
                ),
                ModelProcessProductAIRecognitionResult.rec_type == rec_type,
            )
            .order_by("product_id")
        )
        return [item for item in query_set]

    def query_product_comment_by_product_ids(
        self, product_id_list: list[str]
    ) -> list[ModelFactProductComment]:
        if not product_id_list:
            return []
        query_set = (
            ModelFactProductComment.objects_in(self.dw_db)
            .filter(F.isIn(ModelFactProductComment.product_id, product_id_list))  # type: ignore
            .order_by("product_id")
        )
        return [item for item in query_set]

    def query_product_comment_analysis(
        self,
        product_id_list,
        offset,
        limit,
    ) -> list[ModelFactProductCommentAnalysis]:
        query_set = ModelFactProductCommentAnalysis.objects_in(self.dw_db)
        if product_id_list:
            query_set = query_set.filter(
                F.isIn(ModelFactProductCommentAnalysis.product_id, product_id_list)  # type: ignore
            )
        query_set = query_set.order_by("-update_time", "-comment_uid")[
            offset : offset + limit
        ]
        # 这里不支持ilike，手动改一下
        sql = query_set.as_sql().replace("like(", "ilike(")
        return [x for x in self.dw_db.select(sql)]


def get_chclient():
    db = ChClient()
    try:
        yield db
    finally:
        db.close()


if __name__ == "__main__":
    from app.db.vos.video_overview_schema import VideoSchema

    client = ChClient()
    logger.info("begin")
    videos = client.search_douhot_videos_by_ids(["7431468133732388105"])
    logger.info(f"search_douhot_videos_by_ids:{videos[0].__dict__}")
    for v in videos[:3]:
        videoSchema = VideoSchema().build_by_video(v)
        # logger.info(videoSchema.__dict__)
    videos = client.search_douhot_videos_by_ids(
        ["7431468133732388105"], include_detail=True
    )
    logger.info(f"search_douhot_videos_by_ids:{videos[0].__dict__}")
    for v in videos[:3]:
        videoSchema = VideoSchema().build_by_video(v)
        # logger.info(videoSchema.__dict__)
    video = client.search_douhot_video_info("7431468133732388105")
    logger.info(f"search_douhot_video_info: {video.__dict__}")
    video = client.search_video("特朗普")
    logger.info(f"search_video: {video.__dict__}")

    videos = client.search_page_videos_by_sec_uids([video.author_sec_uid])  # type: ignore
    for v in videos[:3]:
        videoSchema = VideoSchema().build_by_video(v)
        logger.info(videoSchema.__dict__)

    comments = client.query_comments_by_digg_count_desc(
        "MS4wLjABAAAASXO_qbPONjQioUFfkRlo_XiJ2-PK_NUM1GgrwIhnojEEbFHejTABOfLuDiB074xS",
        86400,
    )
    print(comments)

    comment_count = client.get_comment_count(
        "MS4wLjABAAAAKY05ke-y4lvP3OOqo8y2sc1qZ3enW-lRriMdGvcJ9Rq-rgT9Fy5ZPq6zebQdVZ0W"
    )
    print(comment_count)

    videos = client.search_video("酒")
    print(videos)

    video = client.search_video_by_id("7362553788386872639")
    print(video)

    # videos = client.get_today_hot_videos()
    # print(videos)

    video = client.search_videos_by_sec_uids(
        [
            "MS4wLjABAAAAKY05ke-y4lvP3OOqo8y2sc1qZ3enW-lRriMdGvcJ9Rq-rgT9Fy5ZPq6zebQdVZ0W"
        ],
        duration="2",
    )
    print(video)

    marked = client.is_video_marked("7396997210124061993")

    # video = client.get_today_hot_mark_videos()
    # print(video)

    # data = client.search_douhot_account_list(
    #     ["MS4wLjABAAAACEXAhaZyZ8xIfojav3711m4OwmxUz1wGShGA-Ik_yuQ"],
    #     "1",
    # )
    # print(data[0].__dict__)

    # from app.image_text_retrieval.model import embed_text
    # videos = client.search_mark_video_by_frame_embedding(embed_text('超市'))

    pass
