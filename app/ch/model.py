import datetime
import json
import re
from calendar import timegm
from enum import StrEnum

from clickhouse_sqlalchemy.engines import ReplacingMergeTree
from infi.clickhouse_orm import (
    Database,
    NullableField,
    Model,
    DateTimeField,
    StringField,
    MergeTree,
    UInt32Field,
    Float32Field,
    Float64Field,
    UInt64Field,
    Int64Field,
    ArrayField,
    Field,
    F,
    Int32Field,
    Index,
    DateField,
)
from infi.clickhouse_orm.utils import escape
from pydantic import BaseModel

from app.client.s3client import S3Client
from app.logger import logger

VIDEO_SOURCE_DOUYIN = "douyin"
VIDEO_SOURCE_ARTIFICIAL = "artificial"


def JSONExtractInt(col, *attrs):
    return F("JSONExtractInt", col, *attrs)


def JSONExtractString(col, *attrs):
    return F("JSONExtractString", col, *attrs)


class DateTimeEnumEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime.datetime):
            return int(obj.timestamp())
        elif isinstance(obj, Database):
            return None
        return super().default(obj)


class BooleanField(Field):
    # The ClickHouse column type to use
    db_type = "UInt8"

    # The default value
    class_default = False

    def to_python(self, value, timezone_in_use):
        # Convert valid values to bool
        if value in (1, "1", True, "true"):
            return True
        elif value in (0, "0", False, "false"):
            return False
        else:
            raise ValueError("Invalid value for BooleanField: %r" % value)

    def to_db_string(self, value, quote=True):
        # The value was already converted by to_python, so it's a bool
        return "1" if value else "0"


class DictField(Field):
    # The ClickHouse column type to use
    db_type = "String"

    # The default value
    class_default = {}

    def to_python(self, value, timezone_in_use):
        if isinstance(value, dict):
            return value
        else:
            try:
                return json.loads(value)
            except Exception as e:
                logger.error(f"parse as json failed: {e}, value: {value[:1024]}")
                return {}

    def to_db_string(self, value, quote=True):
        db_string = json.dumps(value, ensure_ascii=False)
        # 替换换行符和双引号
        db_string = db_string.replace("\\n", r"\\n")
        db_string = db_string.replace('\\"', r'\\"')
        return db_string


class CrawTimeField(DateTimeField):
    def to_db_string(self, value, quote=True):
        return escape(
            "%010d"
            % timegm(value.replace(minute=0, second=0, microsecond=0).utctimetuple()),
            quote,
        )


class User(Model):
    uid = StringField()  # 97247984806
    # https://p3-pc.douyinpic.com/aweme/100x100/aweme-avatar/mosaic-legacy_2eba60003b1beaf45f6a9.jpeg?from=116350172
    avatar = NullableField(StringField())
    aweme_count = NullableField(UInt32Field())
    enterprise_verify_reason = NullableField(StringField())
    favoriting_count = NullableField(UInt32Field())
    follower_count = UInt32Field()
    nickname = StringField()  # NBA
    # MS4wLjABAAAAGT6rLfDddeLe43Sb9VzfouQjHWZ20BWn16m1h5Zz0ZA  could be append to https://www.douyin.com/user/
    sec_uid = NullableField(StringField())
    signature = NullableField(StringField())  # NBA官方抖音号\nwww.nba.com
    total_favorited = NullableField(UInt32Field())  # 97247984806
    unique_id = NullableField(StringField())
    crawl_time = CrawTimeField()

    engine = MergeTree("uid", ("uid", "nickname", "follower_count", "crawl_time"))

    @classmethod
    def table_name(cls):
        return "user"


class Video(Model):
    aweme_id = StringField()  # 7341957754883493172
    author_user_id = NullableField(UInt64Field())  # 97247984806
    author_sec_uid = NullableField(StringField())
    author_nickname = NullableField(StringField())
    author_avatar_oss_url = NullableField(StringField())
    author = NullableField(DictField())
    collect_count = NullableField(UInt32Field())
    comment_count = NullableField(UInt32Field())
    content = StringField()  # extract text from video
    cover_url = NullableField(StringField())
    cover_oss_url = NullableField(StringField())
    create_time = NullableField(DateTimeField())
    desc = StringField()
    digg_count = UInt32Field()
    download_count = NullableField(UInt32Field())
    duration = NullableField(UInt32Field())
    oss_url = NullableField(StringField())  # sihe oss url for video
    play_count = NullableField(UInt32Field())
    share_count = NullableField(UInt32Field())
    tag = ArrayField(StringField())
    sub_type = ArrayField(StringField())
    text_extra = ArrayField(StringField())
    video_tag = ArrayField(StringField())
    text_embedding = ArrayField(Float32Field())
    image_embedding = ArrayField(Float32Field())
    score = NullableField(Float32Field())
    source = StringField(default=VIDEO_SOURCE_DOUYIN)
    crawl_from = NullableField(StringField())
    crawl_time = CrawTimeField()

    engine = MergeTree(
        "aweme_id", ("aweme_id", "crawl_time", "digg_count", "desc", "content")
    )

    @classmethod
    def table_name(cls):
        return "video"

    def get_play_url(self):
        if self.source and self.source == VIDEO_SOURCE_DOUYIN:
            return "https://www.douyin.com/video/" + self.aweme_id
        elif self.source and self.source == VIDEO_SOURCE_ARTIFICIAL:
            return S3Client().get_artificial_video_play_url(self.aweme_id)
        else:  # 默认还是按抖音处理，避免之前代码有的地方查询的时候没查source
            return "https://www.douyin.com/video/" + self.aweme_id  # 默认还是按抖音处理


class Comment(Model):
    cid = StringField()
    aweme_id = NullableField(StringField())
    create_time = NullableField(DateTimeField())
    digg_count = UInt32Field()
    ip_label = NullableField(StringField())
    is_author_digged = NullableField(BooleanField())
    is_hot = NullableField(BooleanField())
    reply_comment_total = NullableField(UInt32Field())
    text = StringField()
    uid = NullableField(StringField())
    sec_uid = NullableField(StringField())
    sentiment = NullableField(Float32Field())
    crawl_time = CrawTimeField()

    engine = MergeTree("cid", ("cid", "text", "digg_count", "crawl_time"))

    @classmethod
    def table_name(cls):
        return "comment"


class GrantedUser(Model):
    open_id = StringField()
    avatar = NullableField(StringField())
    description = NullableField(StringField())
    nickname = StringField()
    union_id = NullableField(StringField())
    date = NullableField(StringField())
    new_issue = NullableField(UInt32Field())
    new_play = NullableField(UInt32Field())
    total_issue = NullableField(UInt32Field())
    new_fans = NullableField(UInt64Field())
    total_fans = NullableField(UInt64Field())
    new_like = NullableField(UInt64Field())
    new_comment = NullableField(UInt64Field())
    new_share = NullableField(UInt64Field())
    profile_uv = NullableField(UInt64Field())
    fans_data = NullableField(StringField())
    fans_source = NullableField(StringField())
    # play_finish_ratio = NullableField(Float64Field())
    crawl_time = CrawTimeField()

    engine = MergeTree("open_id", ("open_id", "nickname", "crawl_time"))

    @classmethod
    def table_name(cls):
        return "granted_user"


class GrantedUserVideo(Model):
    video_id = StringField()
    open_id = StringField()
    comment_count = NullableField(UInt32Field())
    content = NullableField(StringField())
    cover = NullableField(StringField())
    cover_oss = NullableField(StringField())
    create_time = NullableField(DateTimeField())
    digg_count = NullableField(UInt32Field())
    download_count = NullableField(UInt32Field())
    forward_count = NullableField(UInt32Field())
    is_reviewed = NullableField(BooleanField())
    is_top = NullableField(BooleanField())
    item_id = StringField()
    oss_url = NullableField(StringField())
    play_count = NullableField(UInt32Field())
    share_count = NullableField(UInt32Field())
    title = StringField()
    video_status = NullableField(UInt32Field())
    play_finish_ratio = NullableField(Float64Field())
    crawl_time = CrawTimeField()

    engine = MergeTree("video_id", ("video_id", "open_id", "title", "crawl_time"))

    @classmethod
    def table_name(cls):
        return "granted_user_video"


class CreatorVideo(Model):
    video_id = StringField()
    author_user_id = NullableField(StringField())
    play_finish_ratio = NullableField(Float64Field())
    play_avg_time = NullableField(Float64Field())
    play_count = NullableField(UInt32Field())
    # latest_play_finish_ratio: float = 0
    # latest_play_avg_time: float = 0
    crawl_time = CrawTimeField()

    engine = MergeTree("video_id", ("video_id", "crawl_time"))

    @classmethod
    def table_name(cls):
        return "creator_video"


class CreatorUser(Model):
    unique_id = StringField()
    secret_id = NullableField(StringField())
    open_id = StringField()
    account_region = NullableField(StringField())
    avatar_url = NullableField(StringField())
    follower_count = NullableField(UInt32Field())
    following_count = NullableField(UInt32Field())
    gender = NullableField(UInt32Field())
    location = NullableField(StringField())
    mobile = NullableField(StringField())
    nickname = StringField()
    region = NullableField(StringField())
    school = NullableField(StringField())
    total_favorited = NullableField(UInt32Field())
    overview_data_yesterday = NullableField(StringField())
    overview_data_seven_days = NullableField(StringField())
    overview_data_thirty_days = NullableField(StringField())
    author_diagnosis = NullableField(StringField())
    dashboard = NullableField(StringField())
    crawl_time = CrawTimeField()

    engine = MergeTree("unique_id", ("unique_id", "nickname", "crawl_time"))

    @classmethod
    def table_name(cls):
        return "creator_user"


class Daren(Model):
    user_id = StringField()
    author_sec_uid = StringField()
    user_name = StringField()
    user_head_logo = NullableField(StringField())
    user_gender = NullableField(StringField())
    user_location = NullableField(StringField())
    user_introduction = NullableField(StringField())
    fans_count = UInt32Field()
    like_count = NullableField(UInt64Field())
    item_count = NullableField(UInt32Field())
    first_tag_name = NullableField(StringField())
    second_tag_name = NullableField(StringField())
    aweme_id = NullableField(StringField())
    user_aweme_url = NullableField(StringField())
    aweme_pic = NullableField(StringField())
    user_mile = NullableField(DictField())
    top_video = NullableField(DictField())
    item_mile = NullableField(DictField())
    daren_fans_info = NullableField(DictField())
    crawl_time = CrawTimeField()

    engine = MergeTree("user_id", ("user_id", "user_name", "fans_count", "crawl_time"))

    @classmethod
    def table_name(cls):
        return "daren_info"


VIDEO_MARK_AUDIT_RESULT_PASS = 1
VIDEO_MARK_AUDIT_RESULT_FAIL = 2


class VideoMark(Model):
    aweme_id = StringField()
    create_time = DateTimeField()
    cover_url = NullableField(StringField())
    cover_oss_url = NullableField(StringField())
    desc = NullableField(StringField())
    digg_count = NullableField(UInt32Field())
    mark_script = NullableField(StringField())
    mark_description = NullableField(StringField())
    mark_highlights = NullableField(StringField())
    mark_score = NullableField(UInt32Field())
    mark_keywords = NullableField(StringField())
    mark_script_embedding = ArrayField(Float32Field())
    mark_description_embedding = ArrayField(Float32Field())
    mark_highlights_embedding = ArrayField(Float32Field())
    mark_keywords_embedding = ArrayField(Float32Field())
    mark_hook = NullableField(StringField())
    oss_url = NullableField(StringField())
    content = NullableField(StringField())  # GPTAnnotation.description
    author_avatar_oss_url = NullableField(StringField())
    author_nickname = NullableField(StringField())
    author_sec_uid = NullableField(StringField())
    author_user_id = NullableField(UInt64Field())
    author_topic_keywords = NullableField(StringField())
    author_eye_catching_points = NullableField(StringField())
    author_opening_quality_score = NullableField(Float32Field())
    disassemble = NullableField(StringField())
    mark_time = NullableField(DateTimeField())
    mark_user = NullableField(StringField())
    audit_result = NullableField(UInt32Field())
    source = StringField(default=VIDEO_SOURCE_DOUYIN)
    tag = ArrayField(StringField())
    access_valid = Int32Field(default=1)
    structured_disassemble = NullableField(StringField())

    engine = MergeTree("aweme_id", ("aweme_id", "create_time"))

    @classmethod
    def table_name(cls):
        return "video_mark"

    def get_play_url(self):
        if self.source and self.source == VIDEO_SOURCE_DOUYIN:
            return "https://www.douyin.com/video/" + self.aweme_id
        elif self.source and self.source == VIDEO_SOURCE_ARTIFICIAL:
            return S3Client().get_artificial_video_play_url(self.aweme_id)
        elif self.source and "tiktok" in str(self.source):
            # tiktok视频，url拼接规则不一样
            return (
                f"https://www.tiktok.com/@{self.author_sec_uid}/video/{self.aweme_id}"
            )
        else:  # 默认还是按抖音处理，避免之前代码有的地方查询的时候没查source
            return "https://www.douyin.com/video/" + self.aweme_id  # 默认还是按抖音处理


class VideoToMark(Model):
    uuid = StringField()
    aweme_id = StringField()
    auther_sec_uid = StringField()
    annotate_type = NullableField(StringField())
    annotation = NullableField(StringField())
    annotate_time = NullableField(DateTimeField())
    update_time = NullableField(DateTimeField())
    marked = BooleanField()
    tag = ArrayField(StringField())
    play_url = str
    # 总结项
    gpt_creator_annotation = str
    gpt_annotation = str
    gpt_disassemble = str

    engine = MergeTree("uuid", ("uuid", "aweme_id"))

    @classmethod
    def table_name(cls):
        return "video_to_mark"


class VideoEmbedding(Model):
    aweme_id = StringField()
    frame_id = UInt32Field()
    frame_oss_url = StringField()
    embedding = ArrayField(Float64Field())

    engine = MergeTree("aweme_id", ("aweme_id", "frame_id"))

    @classmethod
    def table_name(cls):
        return "video_embedding"


class UserActionData(Model):
    user_id = UInt64Field()
    action_type = StringField()
    target_type = StringField()
    target_id = StringField()
    action_value = NullableField(Int32Field())
    create_time = DateTimeField()

    engine = MergeTree("user_id", ("user_id", "create_time"))

    @classmethod
    def table_name(cls):
        return "user_action_data"


class DouhotRank(Model):
    last_update_time = DateTimeField()
    crawl_time = DateTimeField()
    douhot_type = StringField()
    douhot_sentence_tag = StringField()
    json_data = NullableField(DictField())

    engine = MergeTree(
        "last_update_time",
        ("last_update_time", "crawl_time", "douhot_type", "douhot_sentence_tag"),
    )

    @classmethod
    def table_name(cls):
        return "douhot_rank_info"


class DouhotAccountInfo(Model):
    sec_uid = StringField()
    author_info = NullableField(DictField())
    account_trends_basic = NullableField(DictField())
    account_trends_graph = NullableField(DictField())
    product_data = NullableField(DictField())
    product_list = NullableField(DictField())
    fans_portrait = NullableField(DictField())
    fans_interest_similar = NullableField(StringField())
    fans_interest_topic = NullableField(StringField())
    fans_interest_search = NullableField(StringField())
    crawl_time = DateTimeField()

    engine = MergeTree("sec_uid", ("sec_uid", "crawl_time"))

    @classmethod
    def table_name(cls):
        return "douhot_account_info"


class DouhotVideoInfo(Model):
    author_sec_id = StringField()
    # TODO: 该表和DouhotAccountInfo无author_user_id信息，会影响关注用户（/follow）
    video_id = StringField()
    video_oss_url = NullableField(StringField())
    cover_oss_url = NullableField(StringField())
    avatar_oss_url = StringField()
    video_basic_info = NullableField(DictField())
    video_trends_basic = NullableField(DictField())
    video_trends_graph = NullableField(DictField())
    comment_word_cloud = NullableField(StringField())
    comment_list = NullableField(DictField())
    audience_portrait = NullableField(DictField())
    crawl_from = StringField()
    sub_type = ArrayField(StringField())
    tag = ArrayField(StringField())
    create_time = Int64Field(
        alias=JSONExtractInt(video_basic_info, "item_model", "create_time")
    )
    digg_count = UInt32Field(alias=JSONExtractInt(video_trends_basic, "like_cnt"))
    crawl_time = DateTimeField()

    aweme_id = str
    oss_url = str
    author_avatar_oss_url = str
    author_sec_uid = str

    source = str
    play_url = str
    cover_url = str
    avatar_url = str
    author_nickname = str
    desc = str

    duration = int
    comment_count = int
    play_count = int
    share_count = int
    like_count = int

    engine = MergeTree("video_id", order_by=("video_id", "author_sec_id", "crawl_time"))

    @classmethod
    def table_name(cls):
        return "douhot_video_info"

    def get_play_url(self):
        return "https://www.douyin.com/video/" + self.video_id  # 默认还是按抖音处理

    def derive_fields(self):
        video_basic_info = self.video_basic_info
        if isinstance(video_basic_info, str):
            video_basic_info = json.loads(video_basic_info)
        elif not isinstance(video_basic_info, dict):
            video_basic_info = {}
        video_trends_basic = self.video_trends_basic
        if isinstance(video_trends_basic, str):
            video_trends_basic = json.loads(video_trends_basic)
        elif not isinstance(video_trends_basic, dict):
            video_trends_basic = {}
        item_model = video_basic_info.get("item_model", {})

        self.aweme_id = self.video_id
        self.oss_url = self.video_oss_url
        self.author_avatar_oss_url = self.avatar_oss_url
        self.author_sec_uid = self.author_sec_id

        self.source = VIDEO_SOURCE_DOUYIN
        self.play_url = DouhotVideoInfo.get_play_url(self)
        self.cover_url = item_model.get("item_cover_url", "")
        self.avatar_url = item_model.get("avatar_url", "")
        self.author_nickname = item_model.get("nick_name", "")
        self.desc = item_model.get("item_title", "")

        self.duration = item_model.get("item_duration", 0)
        self.comment_count = int(video_trends_basic.get("comment_cnt", "") or 0)
        self.play_count = int(video_trends_basic.get("play_cnt", "") or 0)
        self.share_count = int(video_trends_basic.get("share_cnt", "") or 0)
        self.like_count = int(video_trends_basic.get("like_cnt", "") or 0)


class TiktokVideoInfo(Model):
    video_id = StringField()
    author_id = StringField()
    video_url = StringField()
    cover_url = StringField()
    country = StringField()
    platform = StringField()
    video_oss_url = StringField()
    cover_oss_url = StringField()
    crawl_time = DateTimeField()
    tt_type = NullableField(StringField())  # 类型：video或image
    image_oss_urls = NullableField(ArrayField(StringField()))  # 图片OSS地址列表
    audio_oss_url = NullableField(StringField())  # 音频OSS地址
    tt_detail_info = NullableField(StringField())  # 详细信息
    comment_list = NullableField(StringField())  # 评论列表

    engine = MergeTree(
        "video_id", order_by=("video_id", "author_id", "country", "crawl_time")
    )

    @classmethod
    def table_name(cls):
        return "tiktok_video_info"


class EtlNoxProductVideoList(Model):
    video_id = StringField()
    video_data = StringField()
    update_time = DateTimeField()

    @classmethod
    def table_name(cls):
        return "etl_nox_product_video_list"


class EtlNoxDarenVideoList(Model):
    video_id = StringField()
    video_data = StringField()
    update_time = DateTimeField()

    @classmethod
    def table_name(cls):
        return "etl_nox_daren_video_list"


class NoxAccountDetail(Model):
    """Nox账号详情数据"""

    account_id = StringField()  # 账号id
    platform = StringField(default="tiktok")  # 平台
    basic_info = DictField()  # 基本信息
    overview = DictField()  # 数据总览
    product_analysis = DictField()  # 带货分析
    fans_analysis = DictField()  # 粉丝分析(受众分析)
    content_analysis = DictField()  # 内容分析
    brand_analysis = DictField()  # 品牌分析
    crawl_time = DateTimeField()  # 爬虫爬取的时间

    engine = MergeTree("crawl_time", ("account_id", "crawl_time"))

    @classmethod
    def table_name(cls):
        return "nox_account_detail"


class DouhotSentenceInfo(Model):
    sentence_id = StringField()
    sentence_basic_info = NullableField(DictField())
    video_list = NullableField(StringField())
    trends = NullableField(DictField())
    hot_analysis = NullableField(DictField())
    word_cloud = NullableField(StringField())
    comment_list = NullableField(StringField())
    crawl_time = DateTimeField()

    engine = MergeTree("sentence_id", ("sentence_id", "crawl_time"))

    @classmethod
    def table_name(cls):
        return "douhot_sentence_info"


class DouhotTopicInfo(Model):
    topic_id = StringField()
    topic_basic_info = NullableField(DictField())
    video_list = NullableField(StringField())
    trends = NullableField(DictField())
    hot_analysis = NullableField(DictField())
    crawl_time = DateTimeField()

    engine = MergeTree("topic_id", order_by=("topic_id", "crawl_time"))

    @classmethod
    def table_name(cls):
        return "douhot_topic_info"


class DarenDisassembleReport(Model):
    daren_id = StringField()
    source = StringField()
    # doc_id = StringField()
    report_json = DictField()
    # report_markdown = StringField()
    marked_report_json = NullableField(DictField())
    # marked_report_markdown = NullableField(StringField())
    create_time = DateTimeField()
    update_time = DateTimeField()

    engine = MergeTree(date_col="update_time", order_by=("daren_id", "source"))

    @classmethod
    def table_name(cls):
        return "daren_disassemble_report"


class DarenVideoDisassemble(Model):
    author_sec_uid = StringField()
    aweme_id = StringField()
    file_hash = StringField()
    origin_markdown = StringField()  # AI生成的原始markdown内容
    markdown = StringField()  # 标注/审核完成后affine文档转markdown内容
    affine_url = StringField()
    job_type = StringField()
    asr_content = StringField()  # 字幕信息
    disassembling = BooleanField()
    marked = BooleanField()
    passed = BooleanField()
    create_time = DateTimeField()
    update_time = DateTimeField()
    structured_disassemble = NullableField(StringField())

    engine = MergeTree("aweme_id", order_by=("aweme_id",))

    @classmethod
    def table_name(cls):
        return "daren_video_disassemble"


class DarenVideoDisassembleResult(Model):
    file_hash = StringField()
    disassemble_type = StringField()
    source = NullableField(StringField())
    video_info = DictField()
    disassemble_result = DictField()
    create_time = DateTimeField()
    update_time = DateTimeField()

    engine = MergeTree("file_hash", order_by=("file_hash", "disassemble_type"))

    @classmethod
    def table_name(cls):
        return "daren_video_disassemble_result"


class BaikeEntry(Model):
    term_name = StringField()
    description = StringField()
    related_terms = ArrayField(StringField())
    report_links_prod = ArrayField(StringField())
    report_links_staging = ArrayField(StringField())
    report_titles = ArrayField(StringField())
    updated_at = DateTimeField()
    category = StringField()
    status = StringField()
    synonyms = ArrayField(StringField())

    engine = MergeTree("term_name", ("term_name",))

    @classmethod
    def table_name(cls):
        return "liulian_baike_entries"


class FactDouhotTopic(Model):
    topic_id = StringField()  # 抖音话题id
    title = StringField()  # 话题title
    tags = ArrayField(StringField())  # 标签列表
    video_list = ArrayField(StringField())  # 视频列表
    hot_score_7d = Int64Field()  # 7日累计热度值
    like_cnt_7d = Int64Field()  # 7日累计点赞数
    publish_cnt_7d = Int64Field()  # 7日累计投稿数
    play_cnt_7d = Int64Field()  # 7日累计播放数
    publish_cnt_total = Int64Field()  # 总投稿数
    play_cnt_total = Int64Field()  # 总播放数
    avg_play_cnt = Float32Field()  # 稿均播放数
    update_time = DateTimeField()  # 更新时间

    engine = MergeTree(
        date_col="update_time",
        order_by=["topic_id"],
    )

    @classmethod
    def table_name(cls):
        return "fact_douhot_topic"


class DimBrand(Model):
    brand_id = StringField()
    brand_name = StringField()
    brand_name_correct = StringField()
    brand_belong_to = StringField()
    brand_alias = ArrayField(StringField())
    category1_list = ArrayField(StringField())
    label = ArrayField(StringField())
    attribution = StringField()
    attr1 = StringField()
    attr2 = StringField()
    attr3 = StringField()
    attr4 = StringField()
    attr5 = StringField()
    update_time = DateTimeField()

    engine = MergeTree(
        date_col="update_time",
        order_by=["brand_id"],
    )

    @classmethod
    def table_name(cls):
        return "dim_brand"


class DimProduct(Model):
    product_id = StringField()
    product_name = StringField()
    corrected_product_name = StringField()
    product_series = StringField()
    category1 = StringField()
    category2 = StringField()
    category3 = StringField()
    category4 = StringField()
    label = ArrayField(StringField())
    attribution = StringField()
    attr1 = StringField()
    attr2 = StringField()
    attr3 = StringField()
    attr4 = StringField()
    attr5 = StringField()
    update_time = DateTimeField()

    engine = MergeTree(
        date_col="update_time",
        order_by=["product_id"],
    )

    @classmethod
    def table_name(cls):
        return "dim_product"


class DimDaren(Model):
    daren_id = StringField()
    daren_source = StringField()
    daren_name = StringField()
    gender = StringField()
    province = StringField()
    city = StringField()
    style = ArrayField(StringField())
    label = ArrayField(StringField())
    category1_list = ArrayField(StringField())
    attribution = StringField()
    attr1 = StringField()
    attr2 = StringField()
    attr3 = StringField()
    attr4 = StringField()
    attr5 = StringField()
    update_time = DateTimeField()

    engine = MergeTree(
        date_col="update_time",
        order_by=["daren_id"],
    )

    @classmethod
    def table_name(cls):
        return "dim_daren"


class DimShop(Model):
    shop_id = StringField()  # 商店id
    shop_name = StringField()  # 商店名称
    shop_type = StringField()  # 商店类型
    shop_douyin_id = StringField()  # 商店抖音id
    category1_list = ArrayField(StringField())  # 关联类目1列表
    category2_list = ArrayField(StringField())  # 关联类目1列表
    category3_list = ArrayField(StringField())  # 关联类目1列表
    category4_list = ArrayField(StringField())  # 关联类目1列表
    attribution = StringField()  # 附加属性
    attr1 = StringField()  # 展开后附加属性1，便于检索
    attr2 = StringField()  # 展开后附加属性2，便于检索
    attr3 = StringField()  # 展开后附加属性3，便于检索
    attr4 = StringField()  # 展开后附加属性4，便于检索
    attr5 = StringField()  # 展开后附加属性5，便于检索
    update_time = DateTimeField()

    engine = MergeTree(
        date_col="update_time",
        order_by=["shop_id"],
    )

    @classmethod
    def table_name(cls):
        return "dim_shop"


class DimCategory(Model):
    category_id = StringField()
    category_name = StringField()
    parent_id = StringField()
    depth = Int32Field()
    update_time = DateTimeField()

    engine = MergeTree(
        date_col="update_time",
        order_by=["category_id"],
    )

    @classmethod
    def table_name(cls):
        return "dim_category"


class ETLStructuredDouyinVideo(Model):
    aweme_id = StringField()
    cover_oss_url = StringField()
    content = StringField()
    tags = ArrayField(StringField())
    metrics = StringField()
    update_time = DateTimeField()
    rank_type = ArrayField(StringField())
    lines = StringField()
    topic = StringField()
    source = StringField()
    author_name = StringField()

    engine = ReplacingMergeTree("update_time", order_by=["aweme_id"])

    @classmethod
    def table_name(cls):
        return "etl_structured_douyin_video"


class ETLStructuredDouyinDaren(Model):
    daren_id = StringField()
    daren_name = StringField()
    source = StringField()
    tags = ArrayField(StringField())
    is_live = BooleanField()  # 是否为直播达人
    is_video = BooleanField()  # 是否为视频达人
    bring_product_category = ArrayField(StringField())
    bring_product_brand = ArrayField(StringField())
    fans_profile_gender = ArrayField(StringField())
    fans_profile_age = ArrayField(StringField())
    fans_profile_city = ArrayField(StringField())
    fans_profile_province = ArrayField(StringField())
    fans_profile_top_consumer_group = ArrayField(StringField())
    fans_profile_city_level = ArrayField(StringField())
    fans_profile_category_level = ArrayField(StringField())
    fans_profile_fans_interest = ArrayField(StringField())
    live_profile_gender = ArrayField(StringField())
    live_profile_age = ArrayField(StringField())
    live_profile_city = ArrayField(StringField())
    live_profile_province = ArrayField(StringField())
    live_profile_top_consumer_group = ArrayField(StringField())
    live_profile_city_level = ArrayField(StringField())
    live_profile_category_level = ArrayField(StringField())
    live_profile_fans_interest = ArrayField(StringField())
    video_profile_gender = ArrayField(StringField())
    video_profile_age = ArrayField(StringField())
    video_profile_city = ArrayField(StringField())
    video_profile_province = ArrayField(StringField())
    video_profile_top_consumer_group = ArrayField(StringField())
    video_profile_city_level = ArrayField(StringField())
    video_profile_category_level = ArrayField(StringField())
    video_profile_fans_interest = ArrayField(StringField())
    update_time = DateTimeField()

    engine = MergeTree("update_time", order_by=["daren_id"])

    @classmethod
    def table_name(cls):
        return "etl_structured_douyin_daren"


class ModelAggDarenBasicInfo(Model):
    daren_id = StringField()  # 达人ID
    daren_name = StringField()  # 达人名称
    daren_account_id = StringField()  # 达人sec_uid
    source = StringField()  # 达人来源：douyin，xhs
    is_live = BooleanField()  # 是否为直播达人
    is_video = BooleanField()  # 是否为视频达人
    is_bring_product = BooleanField()  # 是否带货达人
    is_official = BooleanField()  # 是否为官方账号(只带货某特定品牌)
    tags = ArrayField(StringField())  # 达人标签：[时尚、美妆、唇部彩妆]
    fans_count_total = Int64Field()  # 粉丝总数
    fans_count_7d = Int64Field()  # 7日粉丝增量
    fans_count_30d = Int64Field()  # 30日粉丝增量
    fans_count_90d = Int64Field()  # 90日粉丝增量
    bring_product_category = ArrayField(StringField())  # 带货品类
    bring_product_brand = ArrayField(StringField())  # 带货品牌
    bring_product_7d = ArrayField(StringField())  # 近7日带货商品信息
    live_bring_product_amount_7d = Float32Field()  # 近7日直播带货总销售额
    live_bring_product_amount_30d = Float32Field()  # 30日直播带货总销售额
    live_bring_product_amount_90d = Float32Field()  # 90日直播带货总销售额
    live_bring_product_volume_7d = Float32Field()  # 7日带货销售量
    live_bring_product_volume_30d = Float32Field()  # 30日带货销售量
    live_bring_product_volume_90d = Float32Field()  # 90日带货销售量
    live_bring_product_rate_7d = Float32Field()  # 近7日直播带货转化率
    live_bring_product_rate_30d = Float32Field()  # 近30日直播带货转化率
    live_bring_product_rate_90d = Float32Field()  # 近90日直播带货转化率
    live_count_total = Float32Field()  # 带货直播总场数
    live_count_7d = Float32Field()  # 7日带货直播场数
    live_count_30d = Float32Field()  # 30日带货直播场数
    live_count_90d = Float32Field()  # 90日带货直播场数
    live_audience_7d = Float32Field()  # 7日观看人数
    live_audience_30d = Float32Field()  # 30日观看人数
    live_audience_90d = Float32Field()  # 90日观看人数
    video_bring_product_amount_7d = Float32Field()  # 7日视频带货销售额
    video_bring_product_amount_30d = Float32Field()  # 30日视频带货销售额
    video_bring_product_amount_90d = Float32Field()  # 90日视频带货销售额
    video_bring_product_volume_7d = Float32Field()  # 7日视频带货销售量
    video_bring_product_volume_30d = Float32Field()  # 30日视频带货销售量
    video_bring_product_volume_90d = Float32Field()  # 90日视频带货销售量
    video_bring_product_gpm_7d = Float32Field()  # 7日gpm(千次播放成交额)
    video_bring_product_gpm_30d = Float32Field()  # 30日gpm(千次播放成交额)
    video_bring_product_gpm_90d = Float32Field()  # 90日gpm(千次播放成交额)
    video_bring_product_ipm_7d = Float32Field()  # 7日ipm(每千次观看的互动数)
    video_bring_product_ipm_30d = Float32Field()  # 30日ipm(每千次观看的互动数)
    video_bring_product_ipm_90d = Float32Field()  # 90日ipm(每千次观看的互动数)
    video_count_total = Float32Field()  # 带货视总数
    video_count_7d = Float32Field()  # 7日带货视总数
    video_count_30d = Float32Field()  # 30日带货视总数
    video_count_90d = Float32Field()  # 90日带货视总数
    works_play_7d = Int64Field()  # 7日作品播放/浏览数
    works_play_30d = Int64Field()  # 30日作品播放/浏览数
    works_play_90d = Int64Field()  # 90日作品播放/浏览数
    works_comment_7d = Int64Field()  # 7日作品评论数
    works_comment_30d = Int64Field()  # 30日作品评论数
    works_comment_90d = Int64Field()  # 90日作品评论数
    works_share_7d = Int64Field()  # 7日作品分享数
    works_share_30d = Int64Field()  # 30日作品分享数
    works_share_90d = Int64Field()  # 90日作品分享数
    works_like_7d = Int64Field()  # 7日作品点赞数
    works_like_30d = Int64Field()  # 30日作品点赞数
    works_like_90d = Int64Field()  # 90日作品点赞数
    works_interaction_rate_7d = Float32Field()  # 7日作品互动率
    works_interaction_rate_30d = Float32Field()  # 30日作品互动率
    works_interaction_rate_90d = Float32Field()  # 90日作品互动率
    fans_profile_info = (
        StringField()
    )  # 粉丝画像(性别，年龄，地域，省份，消费人群，城市等级，兴趣内容)
    live_profile_info = (
        StringField()
    )  # 直播观众画像(性别，年龄，地域、省份、消费人群，城市等级，价格区间)
    video_profile_info = (
        StringField()
    )  # 视频观众画像(性别，年龄，城市、省份、消费人群，城市等级，价格区间)
    data_source = StringField()
    data_platform = StringField()
    update_time = DateTimeField()  # 更新时间

    engine = MergeTree(
        date_col="update_time",
        order_by=["daren_id"],
    )

    @classmethod
    def table_name(cls):
        return "agg_daren_basic_info"


class AggProductBaseInfo(Model):
    product_id = StringField()
    product_name = StringField()
    corrected_product_name = StringField()
    product_series = StringField()
    category1 = StringField()
    category2 = StringField()
    category3 = StringField()
    category4 = StringField()

    engine = MergeTree(
        date_col="update_time",
        order_by=["product_id"],
    )

    @classmethod
    def table_name(cls):
        return "agg_product_base_info"


class ModelFactProductCommentAnalysis(Model):
    brand_id = StringField()
    brand_name = StringField()
    shop_id = StringField()
    shop_name = StringField()
    category1 = StringField()
    category2 = StringField()
    category3 = StringField()
    category4 = StringField()
    product_id = StringField()
    product_name = StringField()
    comment_uid = StringField()
    comment_data = StringField()
    likes = Float32Field()
    score = Float32Field()
    weighting_score = Float32Field()
    people_tags = StringField()
    profession_tags = StringField()
    moment_tags = StringField()
    location_tags = StringField()
    purpose_tags = StringField()
    scene_tags = StringField()
    positive_tags = StringField()
    negative_tags = StringField()
    motivation_tags = StringField()
    unsatisfied_tags = StringField()
    data_source = StringField()
    data_platform = StringField()
    update_time = DateTimeField()

    engine = MergeTree(date_col="update_time", order_by=["product_id", "comment_uid"])

    @classmethod
    def table_name(cls):
        return "fact_product_comment_analysis"


class DictVideoAttributes(Model):
    hash_key = StringField()
    category = StringField()  # 视频分类
    key = StringField()  # 属性key (英文, 如 content_type)
    name = StringField()  # 属性名称 (中文, 内容方向)
    value = StringField()  # 属性值 (如 买房攻略)
    count = Int32Field()  # 对应视频数量
    update_time = DateTimeField(default=datetime.datetime.fromtimestamp(0))

    engine = MergeTree(date_col="update_time", order_by=["hash_key"])

    @classmethod
    def table_name(cls):
        return "dict_video_attributes"


class DictVideoAttributesV2(Model):
    hash_key = StringField()
    category1 = StringField()  # 一级分类
    category2 = StringField()  # 二级分类
    key = StringField()  # 属性key (英文, 如 content_type)
    name = StringField()  # 属性名称 (中文, 内容方向)
    value = StringField()  # 属性值 (如 买房攻略)
    count = Int32Field()  # 对应视频数量
    update_time = DateTimeField(default=datetime.datetime.fromtimestamp(0))

    engine = MergeTree(date_col="update_time", order_by=["hash_key"])

    @classmethod
    def table_name(cls):
        return "dict_video_attributes_v2"


class DictProductAttributes(Model):
    hash_key = StringField()
    category1 = StringField()  # 一级分类
    category2 = StringField()  # 二级分类
    category3 = StringField()  # 三级分类
    category4 = StringField()  # 四级分类
    key = StringField()  # 属性key (英文, 如 degree)
    name = StringField()  # 属性名称 (中文, 如度数)
    value = StringField()  # 属性值 (如 52, 55.2)
    count = Int32Field()  # 对应商品数量
    update_time = DateTimeField(default=datetime.datetime.fromtimestamp(0))

    engine = MergeTree(
        date_col="update_time",
        order_by=["category1", "category2", "category3", "category4", "key", "value"],
    )

    @classmethod
    def table_name(cls):
        return "dict_product_attributes"


class XinhongAccountDetail(Model):
    account_id = StringField()
    basic_info = StringField()
    overview = StringField()
    notes_analysis = StringField()
    fans_analysis = StringField()
    comments_analysis = StringField()
    brand_analysis = StringField()
    cate_analysis = StringField()
    live_analysis = StringField()
    crawl_time = DateTimeField()

    # engine = MergeTree(
    #     primary_key=["account_id"], order_by=["account_id", "crawl_time"]
    # )

    @classmethod
    def table_name(cls):
        return "xinhong_account_detail"


class XiaohongshuNoteInfo(Model):
    note_id = StringField()
    account_id = StringField()
    note_type = StringField()
    video_oss_url = StringField()
    cover_oss_url = StringField()
    avatar_oss_url = StringField()
    image_oss_urls = ArrayField(StringField())
    note_basic_info = StringField()
    note_detail_info = StringField()
    comment_list = StringField()
    tag = ArrayField(StringField())
    crawl_time = DateTimeField()

    # engine = MergeTree(
    #     primary_key=["note_id", "account_id", "note_type"],
    #     order_by=["note_id", "account_id", "note_type", "crawl_time"],
    # )

    @classmethod
    def table_name(cls):
        return "xiaohongshu_notes_info"


class XinhongAccountHistoryNotes(Model):
    account_id = StringField()  # 账号id
    start_time = StringField()  # 列表起始时间（按季度）
    notes = StringField()  # 笔记列表
    hot_note = StringField()  # 爆款笔记
    hot_info = StringField()  # 爆款信息
    crawl_time = DateTimeField()  # 爬虫爬取的时间

    engine = ReplacingMergeTree(
        date_col="crawl_time",
        primary_key=("account_id", "start_time"),
        order_by=("account_id", "start_time"),
    )

    @classmethod
    def table_name(cls):
        return "xinhong_account_history_notes"


class ModelAggProductBaseInfo(Model):
    brand_id = StringField()
    product_id = StringField()
    product_name = StringField()
    corrected_product_name = StringField()
    product_series = StringField()
    spu_name = StringField()
    price_range = StringField()
    market_class = ArrayField(StringField())
    category1 = StringField()
    category2 = StringField()
    category3 = StringField()
    category4 = StringField()
    promotion_info = ArrayField(StringField())
    services_info = ArrayField(StringField())
    delivery_location = StringField()
    delivery_service = StringField()
    conversation_rate_30d = Float32Field()
    pv_count_30d = Float32Field()
    attributes = StringField()
    data_source = StringField()
    data_platform = StringField()
    update_time = DateTimeField()

    engine = MergeTree(
        date_col="update_time",
        order_by=["product_id"],
    )

    @classmethod
    def table_name(cls):
        return "agg_product_base_info"


# 商品AI识别记录
class ModelProcessProductAIRecognitionResult(Model):
    product_id = StringField()
    product_name = StringField()
    category1 = StringField()
    category2 = StringField()
    category3 = StringField()
    category4 = StringField()
    rec_type = StringField()
    rec_result = StringField()
    update_time = DateTimeField()

    engine = MergeTree(date_col="update_time", order_by=["product_id"])

    @classmethod
    def table_name(cls):
        return "process_product_ai_recognition_result"


# 视频AI识别记录
class ModelProcessVideoAIRecognitionResult(Model):
    video_id = StringField()
    rec_type = StringField()
    rec_result = StringField()
    update_time = DateTimeField()

    engine = MergeTree(date_col="update_time", order_by=["video_id"])

    @classmethod
    def table_name(cls):
        return "process_video_ai_recognition_result"


class ModelFactProductComment(Model):
    product_id = StringField()  # 商品ID
    product_name = StringField()  # 商品名称
    category1 = StringField()  # 类目1
    category2 = StringField()  # 类目2
    category3 = StringField()  # 类目3
    category4 = StringField()  # 类目4
    attribution = StringField()  # 附加属性，用于存放logo、description等商品动态属性
    attr1 = StringField()  # 展开后附加属性1，便于检索
    attr2 = StringField()  # 展开后附加属性2，便于检索
    attr3 = StringField()  # 展开后附加属性3，便于检索
    attr4 = StringField()  # 展开后附加属性4，便于检索
    attr5 = StringField()  # 展开后附加属性5，便于检索
    comment_cnt = Float32Field()  # 评价数
    positive_cnt = Float32Field()  # 好评数
    positive_rate = Float32Field()  # 好评率
    negative_cnt = Float32Field()  # 差评数
    negative_rate = Float32Field()  # 差评率
    positive_example = StringField()  # 好评案例
    negative_example = StringField()  # 差评案例
    data_source = StringField()
    data_platform = StringField()
    update_time = DateTimeField()

    engine = MergeTree(
        date_col="update_time",
        order_by=["product_id"],
    )

    @classmethod
    def table_name(cls):
        return "fact_product_comment"


class EmbeddingTextType(StrEnum):
    DarenName = "达人名称"
    ShopName = "店铺名称"
    ProductAttributes = "商品属性"


class EmbeddingTextItem(BaseModel):
    """用于内存中表示与传递 EmbeddingText"""

    text: str
    json_metadata: str = "{}"
    cosine_distance: float = 0.0

    def __hash__(self) -> int:
        return hash((self.text, self.json_metadata))


class EmbeddingText(Model):
    # EmbeddingTextType
    text_type = StringField()
    text = StringField()
    update_time = DateTimeField()
    embedding_vector = ArrayField(Float32Field())
    json_metadata = StringField()

    vector_idx = Index(
        type='vector_similarity("hnsw", "cosineDistance", 1024)',
        expr=embedding_vector,
        granularity=1000,
    )

    engine = MergeTree(
        date_col="update_time",
        order_by=["text_type", "update_time"],
    )

    @classmethod
    def table_name(cls):
        return "embedding_text_v3"


class EmbeddingTextWithCosineDistance(EmbeddingText):
    cosine_distance = Float32Field()


class DwsAttributesMapping(Model):
    text_type = StringField()
    text = StringField()
    embedding_vector = ArrayField(Float32Field())
    mapped_text = StringField()
    json_metadata = StringField()
    update_time = DateTimeField(default=datetime.datetime.fromtimestamp(0))

    engine = MergeTree(date_col="update_time", order_by=["text_type", "text"])

    @classmethod
    def table_name(cls):
        return "dws_attributes_mapping"


class DwsAttributesMappingWithCosineDistance(DwsAttributesMapping):
    cosine_distance = Float32Field()


class FeedCardContentInfo(Model):
    card_id = StringField()
    card_type = StringField()
    card_desc = StringField()
    start_date = DateField()
    end_date = DateField()
    title = StringField()
    content = StringField()
    update_time = DateTimeField()
    sub_title = StringField()
    card_name = StringField()
    category1 = StringField()
    category2 = StringField()
    category3 = StringField()
    category4 = StringField()
    data_platform = StringField()
    push_time = DateTimeField()

    engine = MergeTree("update_time", order_by=["card_id"])

    @classmethod
    def table_name(cls):
        return "feed_card_content_info"


class ModelAggVideoBaseInfoV2(Model):
    video_id = StringField()
    desc = StringField()
    author_account_id = StringField()
    author_name = StringField()
    daren_id = StringField()
    author_fans_cnt = Float32Field()
    rank_type = ArrayField(StringField())
    bring_goods = ArrayField(StringField())
    promotion_type = StringField()
    is_bring_goods = BooleanField()
    hot_comment_words = ArrayField(StringField())
    categories = ArrayField(StringField())
    sub_categories = ArrayField(StringField())
    attributes = StringField()
    audience_profile = StringField()
    like_cnt = Float32Field()
    comment_cnt = Float32Field()
    share_cnt = Float32Field()
    favourite_cnt = Float32Field()
    view_cnt = Float32Field()
    sales_amount = Float32Field()
    sales_volume = Float32Field()
    duration = Float32Field()
    create_date = DateField()
    create_time = DateTimeField()
    data_source = StringField()
    data_platform = StringField()
    update_time = DateTimeField()

    engine = MergeTree(
        date_col="update_time",
        order_by=["video_id"],
    )

    @classmethod
    def table_name(cls):
        return "agg_video_base_info_v2"


# 用户活动监控系统相关模型


class UsersWhitelist(Model):
    """用户白名单表 - datawarehouse.users_whitelist"""

    phone = StringField()  # 手机号（用户标识）
    customer_name = StringField()  # 客户名称（看板显示用）
    env_source = StringField()  # 数据来源环境：staging | prod
    user_id = NullableField(UInt32Field())  # 用户ID，通过手机号查询获得，可能为空
    is_internal_user = BooleanField()  # 是否内部人员（True=是，统计时排除）
    additional = (
        StringField()
    )  # 扩展信息JSON: {"add_date": "2025-01-01", "added_by": "张三", "ecommerce_category": "服装", "content_category": "美妆", "reading_category": "小说"}
    created_at = DateTimeField()
    updated_at = DateTimeField()

    engine = ReplacingMergeTree(
        version_col="updated_at",
        order_by=["env_source", "phone"],
    )

    @classmethod
    def table_name(cls):
        return "users_whitelist"


class OdsUserActivityEvents(Model):
    """用户活动事件表 - datawarehouse.ods_user_activity_events"""

    event_time = DateTimeField()  # 事件发生时间，用于按日分区
    event_name = StringField()  # 事件名称（如：SEND_MESSAGE, EXPORT_PDF等），作为索引
    user_id = StringField()  # 用户ID
    source = StringField()  # 事件来源：web（前端） | backend（后端）
    event_args = StringField()  # 事件参数JSON，存储具体业务数据
    user_agent = StringField()  # 用户代理信息
    client_ip = StringField()  # 客户端IP地址

    engine = MergeTree(
        date_col="event_time",
        order_by=["event_name", "event_time", "user_id"],
    )

    @classmethod
    def table_name(cls):
        return "ods_user_activity_events"
