from enum import StrEnum, IntEnum


class SenderType(StrEnum):
    user = "user"
    agent = "agent"


class ViewActionEnum(StrEnum):
    enter = "enter"
    viewing = "viewing"
    leave = "leave"


class ChatStatus(StrEnum):
    done = "done"
    ongoing = "ongoing"
    error = "error"
    abort = "abort"

    @staticmethod
    def iscomplete(status):
        return status in {ChatStatus.done, ChatStatus.error, ChatStatus.abort}


class ChatCommand(StrEnum):
    retry = "retry"  # 重新回答
    abort = "abort"  # 停止回答
    voting = "voting"  # 点赞/点踩


class ChatVoting(StrEnum):
    up = "up"
    down = "down"
    idle = ""


class TaskType(StrEnum):
    select = "select"
    derive = "derive"
    make_checklist = "make_checklist"
    estimate_cost = "estimate_cost"
    derive_outline = "derive_outline"
    derive_audience = "derive_audience"
    derive_detail = "derive_detail"
    suggest_similar = "suggest_similar"
    success_case = "success_case"
    ai_chat = "ai_chat"
    read_report = "read_report"
    product_report = "product_report"


class TaskStatus(StrEnum):
    done = "done"
    ongoing = "ongoing"
    error = "error"


class DataPeriod(StrEnum):
    last_7_days = "1"
    last_30_days = "2"


class UserActionType(StrEnum):
    like = "like"
    collect = "collect"
    follow = "follow"
    play_time = "play_time"


class UserActionTarget(StrEnum):
    video = "video"
    account = "account"


class FollowStatus:
    follow = "follow"
    unfollow = "unfollow"


class UserType(StrEnum):
    normal = "normal"
    admin = "admin"


class BillboardKey(StrEnum):
    hot = "hot"
    challenge = "challenge"
    cuisine = "cuisine"
    travel = "travel"
    plot = "plot"
    others = "others"
    topic = "topic"  # 话题榜


class SizeUnit:
    KB: int = 1024
    MB: int = 1024 * KB
    GB: int = 1024 * MB
    TB: int = 1024 * GB


class ContentType(StrEnum):
    init = "init"
    normal = "normal"
    command = "command"
    decision = "decision"  # 选择题答案
    disasm_request = "disasm_request"
    disasm_report = "disasm_report"
    knowledge_base = "knowledge_base"
    product_report = "product_report"
    choice = "choice"  # 选择题
    derive_form = "derive_form"  # 拍同款表格
    derive_confirm = "derive_confirm"  # 用户填完拍同款表格
    clear_context = "clear_context"  # 清除会话上下文
    card = "card"  # 卡片类型消息
    video_imitation_task = "video_imitation_task"  # 新版视频仿拍流程


class ChatType(StrEnum):
    plain = "plain"  # 仅包含文本
    document = "document"  # 包含文档，可能也包含文本
    video = "video"  # 包含视频，可能也包含文本
    image = "image"  # 包含图片，可能也包含文本
    link = "link"  # 包含链接，可能也包含文本


class DeriveStage(StrEnum):
    init = "init"
    outline = "outline"
    audience = "audience"
    detail = "detail"
    done = "done"


class ChatSentence(StrEnum):
    init_how_upload = "如何上传一段视频？"
    init_how_many = "我最多可以上传几条视频？"
    init_how_long = "拆解一段视频大约需要多久？"
    init_how_derive = "如何开始做同款视频？"
    derive_same_style = "我要拍同款"
    derive_same_style_outline = "视频主题内容是什么？"
    derive_same_style_audience = "视频营销对象是谁？"
    derive_same_style_detail = "视频重点突出什么？"
    make_checklist = "生成拍摄清单"
    estimate_cost = "预估拍摄成本"
    suggest_similar = "推荐类似风格短视频"
    success_case = "相关成功案例"


# 助手名称，
# 综合对话(搜索视频库+知识库): general
# 大模型聊天(仅gpt): normal，
# 视频库搜索(仅搜索视频库): video，
# 文档聊天: document，
# 策划助手: planner，
# 阅读助手总结：read
# 产品助手: product
class AssistantType(StrEnum):
    general = "general"
    normal = "normal"
    video = "video"
    document = "document"
    planner = "planner"
    read = "read"
    product = "product"


# 爬虫数据来源
class CrawlFromType(StrEnum):
    douhot_video = "热点宝-视频榜"


class CookiePlatform(StrEnum):
    douhot = "douhot"
    luopan = "luopan"
    luopan_qita = "luopan_qita"
    douyin = "douyin"
    chanmama = "chanmama"
    xinhong = "xinhong"
    xiaohongshu = "xiaohongshu"
    trendinsight = "trendinsight"
    nox = "nox"
    tiktok = "tiktok"
    youtube = "youtube"
    instagram = "instagram"
    amazon = "amazon"
    facebook = "facebook"
    twitter = "twitter"
    bilibili = "bilibili"
    temp1 = "temp1"
    temp2 = "temp2"
    temp3 = "temp3"


class AffineBlockReferenceType(StrEnum):
    image = "image"
    chart = "chart"
    database = "database"


class KnowledgeBaseType(StrEnum):
    research = "research"  # 研报库
    user = "user"  # 用户库
    report = "report"  # 报告库
    template = "template"  # 模板库
    video = "video"  # 视频素材库
    text = "text"  # 普通文本库
    daren = "daren"  # 达人信息库


class DocumentPermission(IntEnum):
    deny = -1  # 只用于权限判断，DB里不会写这个值
    read = 0
    list = 0  # 只用于权限判断，DB里不会写这个值
    write = 1
    admin = 10
    owner = 99
