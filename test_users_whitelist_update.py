#!/usr/bin/env python3
"""
测试 UsersWhitelist 模型更新
验证 added_by 字段已移到 additional JSON 中，并使用 ReplacingMergeTree 引擎
"""

import json
from datetime import datetime

# 模拟测试数据
def test_users_whitelist_structure():
    """测试用户白名单数据结构（支持多环境不去重）"""

    # 模拟同一手机号在不同环境的数据
    test_cases = [
        {
            "env": "staging",
            "row": ["2025-01-01", "13800138000", "张三公司(测试)", "李四", "服装", "美妆", "小说", "否"]
        },
        {
            "env": "prod",
            "row": ["2025-01-01", "13800138000", "张三公司(正式)", "王五", "数码", "护肤", "漫画", "否"]
        }
    ]

    records = []

    for case in test_cases:
        feishu_row = case["row"]
        env_name = case["env"]

        # 模拟解析后的数据结构
        phone = feishu_row[1].strip()
        customer_name = feishu_row[2].strip()
        added_by = feishu_row[3].strip()
        ecommerce_category = feishu_row[4].strip()
        content_category = feishu_row[5].strip()
        reading_category = feishu_row[6].strip()
        internal_flag = feishu_row[7].strip()

        # 构建扩展信息JSON
        additional = {
            "added_by": added_by,
            "ecommerce_category": ecommerce_category,
            "content_category": content_category,
            "reading_category": reading_category,
        }

        is_internal_user = internal_flag == "是"

        # 最终的数据结构（env_source + phone 组合唯一）
        record = {
            "phone": phone,
            "customer_name": customer_name,
            "env_source": env_name,
            "is_internal_user": is_internal_user,
            "additional": json.dumps(additional, ensure_ascii=False),
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        }

        records.append(record)

    print("=== 测试用户白名单数据结构（多环境支持）===")
    for i, record in enumerate(records):
        print(f"\n记录 {i+1}:")
        print(f"  手机号: {record['phone']}")
        print(f"  客户名称: {record['customer_name']}")
        print(f"  环境来源: {record['env_source']}")
        print(f"  是否内部用户: {record['is_internal_user']}")
        print(f"  扩展信息: {record['additional']}")

        # 验证每条记录
        additional_data = json.loads(record['additional'])
        assert "added_by" in additional_data
        assert "env_source" in record

    print("\n✅ 数据结构验证通过")
    print("✅ 同一手机号可以在不同环境中存在")
    print("✅ env_source + phone 组合唯一")
    print("✅ 不进行去重处理")

    return records

def test_clickhouse_table_structure():
    """测试 ClickHouse 表结构"""

    expected_sql = """
    CREATE TABLE datawarehouse.users_whitelist (
        phone String,                    -- 手机号（用户标识）
        customer_name String,            -- 客户名称（看板显示用）
        env_source String,               -- 数据来源环境：staging | prod
        is_internal_user Boolean,        -- 是否内部人员（true=是，统计时排除）
        additional String,               -- 扩展信息JSON: {"add_date": "2025-01-01", "added_by": "张三", "ecommerce_category": "服装", "content_category": "美妆", "reading_category": "小说"}
        created_at DateTime DEFAULT now(),
        updated_at DateTime DEFAULT now()
    ) ENGINE = ReplacingMergeTree(updated_at)
    ORDER BY (env_source, phone);
    """

    print("\n=== 测试 ClickHouse 表结构 ===")
    print("预期的表结构:")
    print(expected_sql.strip())

    # 验证关键点
    assert "added_by String" not in expected_sql  # 不应该有独立的 added_by 字段
    assert "env_source String" in expected_sql  # 应该有 env_source 字段
    assert "ReplacingMergeTree(updated_at)" in expected_sql  # 使用 ReplacingMergeTree
    assert "ORDER BY (env_source, phone)" in expected_sql  # 按环境+手机号排序
    assert '"added_by"' in expected_sql  # additional 注释中包含 added_by 示例

    print("✅ 表结构验证通过")
    print("✅ 使用 ReplacingMergeTree 引擎")
    print("✅ 不包含独立的 added_by 字段")
    print("✅ 新增 env_source 字段标识环境来源")
    print("✅ 使用 (env_source, phone) 复合排序键")
    print("✅ additional 字段包含 added_by 信息")

if __name__ == "__main__":
    test_users_whitelist_structure()
    test_clickhouse_table_structure()
    print("\n🎉 所有测试通过！用户白名单表结构更新成功！")
    print("📊 支持多环境数据同步，env_source + phone 组合唯一")
    print("🔄 不进行去重，保留所有环境的数据")
