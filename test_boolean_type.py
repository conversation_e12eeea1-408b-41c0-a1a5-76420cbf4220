#!/usr/bin/env python3
"""
测试 Boolean 类型在用户白名单中的使用
"""

import json
from datetime import datetime

def test_boolean_usage():
    """测试 Boolean 类型的正确使用"""
    
    # 测试数据
    test_cases = [
        {"internal_flag": "是", "expected": True},
        {"internal_flag": "否", "expected": False},
        {"internal_flag": "yes", "expected": False},  # 只有"是"才是true
        {"internal_flag": "", "expected": False},
    ]
    
    print("=== 测试 Boolean 类型使用 ===")
    
    for i, case in enumerate(test_cases):
        internal_flag = case["internal_flag"]
        expected = case["expected"]
        
        # 模拟解析逻辑
        is_internal_user = internal_flag == "是"
        
        # 验证结果
        assert is_internal_user == expected, f"测试用例 {i+1} 失败: {internal_flag} -> {is_internal_user}, 期望: {expected}"
        
        print(f"✅ 测试用例 {i+1}: '{internal_flag}' -> {is_internal_user}")
        
        # 模拟数据记录
        record = {
            "phone": "13800138000",
            "customer_name": "测试公司",
            "env_source": "prod",
            "is_internal_user": is_internal_user,  # Boolean 类型
            "additional": json.dumps({"test": "data"}, ensure_ascii=False),
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        }
        
        # 验证记录中的 Boolean 类型
        assert isinstance(record["is_internal_user"], bool), "is_internal_user 应该是 Boolean 类型"
    
    print("\n✅ 所有 Boolean 类型测试通过")
    print("✅ 只有 '是' 会被解析为 true")
    print("✅ 其他所有值都被解析为 false")

def test_clickhouse_boolean_values():
    """测试 ClickHouse Boolean 值的格式"""
    
    print("\n=== 测试 ClickHouse Boolean 值格式 ===")
    
    test_values = [True, False]
    
    for value in test_values:
        # 转换为 ClickHouse Boolean 字符串
        ch_boolean = "true" if value else "false"
        
        print(f"Python {value} -> ClickHouse {ch_boolean}")
        
        # 验证格式
        assert ch_boolean in ["true", "false"], "ClickHouse Boolean 值应该是 'true' 或 'false'"
    
    print("✅ ClickHouse Boolean 格式正确")

if __name__ == "__main__":
    test_boolean_usage()
    test_clickhouse_boolean_values()
    print("\n🎉 Boolean 类型测试全部通过！")
